import { PrismaClient } from "@prisma/client";
import { calculatePlayerPoints } from "./utils";
import { UserGWSheet, PlayerGWStats, Player } from "@prisma/client";

const prisma = new PrismaClient();

export interface GameweekTransitionResult {
  success: boolean;
  message: string;
  previousGW?: string;
  newGW?: string;
  updatedUserGWSheets?: number;
}

// Define proper types for the functions
type PlayerGWStatsWithPlayer = PlayerGWStats & { player: Player };

export async function getAutoSub(
  starterId: string,
  starterStats: PlayerGWStatsWithPlayer[],
  subStats: PlayerGWStatsWithPlayer[]
): Promise<PlayerGWStatsWithPlayer | null> {
  const starterStat = starterStats.find((s) => s.playerId === starterId);
  if (!starterStat) {
    throw new Error(`Starter stat not found for player ${starterId}`);
  }
  const autoSubStat = subStats.find((sub) => sub.player.position === starterStat.player.position);
  if (!autoSubStat) {
    throw new Error(`Auto-sub not found for player ${starterId}`);
  }
  return autoSubStat;
}


/**
 * Calculate total points for a user's gameweek team
 */
export async function updateUserGWSheet(
  userGWSheet: UserGWSheet
): Promise<UserGWSheet> {
  try {
    if (!userGWSheet) {
      throw new Error(`UserGWSheet not found`);
    }

    let captainId = userGWSheet.captainId;
    let viceCaptainId = userGWSheet.viceCaptainId;

    let starterStats: PlayerGWStatsWithPlayer[] = await prisma.playerGWStats.findMany({
      where: { playerId: { in: userGWSheet.starters }, GW: userGWSheet.GW },
      include: { player: true },
    });

    let subStats: PlayerGWStatsWithPlayer[] = await prisma.playerGWStats.findMany({
      where: { playerId: { in: userGWSheet.subs }, GW: userGWSheet.GW },
      include: { player: true },
    });

    for (const starterStat of starterStats) {
      if (starterStat.minutesPlayed === 0) {
        if (starterStat.playerId === captainId) {
          captainId = viceCaptainId;
          viceCaptainId = null;
        }
        if (starterStat.playerId === viceCaptainId) {
          viceCaptainId = null;
        }
        console.log(`Auto-substituting ${starterStat.player.name} for ${starterStat.player.name}`);
        const autoSubStats = await getAutoSub(starterStat.playerId, starterStats, subStats);
        if (autoSubStats) {
          // Remove auto-sub from starters and add to subs
          starterStats = starterStats.map((starter) => starter.playerId !== starterStat.playerId ? starter : autoSubStats);
          subStats = subStats.map((sub) => sub.playerId !== autoSubStats.playerId ? sub : starterStat);
        }
      }
    }

    const starterPoints = starterStats.reduce((sum, starter) => {
      let playerPoints = calculatePlayerPoints(starter, starter.player.position, starter.playerId === captainId);
      if (starter.playerId === captainId) {
        playerPoints *= 2;
      }
      return sum + playerPoints;
    }, 0);

    const subPoints = subStats.reduce((sum, sub) => {
      return sum + calculatePlayerPoints(sub, sub.player.position);
    }, 0);


    const captainStat = starterStats.find((starter) => starter.playerId === captainId);
    const viceCaptainStat = starterStats.find((starter) => starter.playerId === viceCaptainId);
    const captainPoints = captainStat ? calculatePlayerPoints(captainStat, captainStat.player.position, true) : 0;
    const viceCaptainPoints = viceCaptainStat ? calculatePlayerPoints(viceCaptainStat, viceCaptainStat.player.position, false) : 0;

    const updatedUserGWSheet = await prisma.userGWSheet.update({
      where: { id: userGWSheet.id },
      data: {
        captainId,
        viceCaptainId,
        starters: starterStats.map((starter) => starter.playerId),
        subs: subStats.map((sub) => sub.playerId),
        totalPoints: starterPoints,
        pointsOnBench: subPoints,
        updatedAt: new Date(),
        captainPoints,
        viceCaptainPoints,
      },
    });


    return updatedUserGWSheet;
  } catch (error) {
    console.error("Error updating userGWSheet:", error);
    throw error;
  }
}

/**
 * Finish current gameweek and calculate all user points
 */
export async function finishGameweek(
  gwId: string
): Promise<GameweekTransitionResult> {
  try {
    // Step 1: Get all user sheets BEFORE transaction
    const userGWSheets = await prisma.userGWSheet.findMany({
      where: { GW: gwId },
      include: { user: true },
    });

    const currentGW = await prisma.gameWeek.findUnique({ where: { id: gwId } });

    if (!currentGW || currentGW.status !== "LIVE") {
      throw new Error(`Gameweek ${gwId} not in correct state`);
    }

    // Update userGWSheets

    const updatedUserGWSheets: UserGWSheet[] = await Promise.all(
      userGWSheets.map(async (userGWSheet) => {
        return updateUserGWSheet(userGWSheet);
      })
    );

    // Step 3: Transaction for only the DB updates
    const result = await prisma.$transaction(async (tx) => {
      for (const { userId, totalPoints: gwPoints } of updatedUserGWSheets) {
        await tx.user.update({
          where: { id: userId },
          data: {
            totalPoints: { increment: gwPoints },
          },
        });
      }

      await tx.gameWeek.update({
        where: { id: gwId },
        data: {
          status: "FINISHED",
          isActive: false,
        },
      });
      // activate next gameweek
      const nextGW = await tx.gameWeek.findFirst({
        where: { season: currentGW.season, GW: currentGW.GW + 1 },
      });

      if (nextGW) {
        await tx.gameWeek.update({
          where: { id: nextGW.id },
          data: { isActive: true, status: "UPCOMING" },
        });
      }

      return {
        updatedUserGWSheets: updatedUserGWSheets.length,
        currentGW,
      };
    });

    return {
      success: true,
      message: `Gameweek ${result.currentGW.GW} finished successfully`,
      previousGW: gwId,
      updatedUserGWSheets: result.updatedUserGWSheets,
    };
  } catch (error) {
    console.error("Error finishing gameweek:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Start next gameweek
 */
export async function activateGameweek(
  GwId: string
): Promise<GameweekTransitionResult> {
  try {
    const result = await prisma.$transaction(async (tx) => {
      // Deactivate current gameweek
      await tx.gameWeek.updateMany({
        where: { isActive: true },
        data: { isActive: false },
      });

      // Activate next gameweek
      const nextGW = await tx.gameWeek.update({
        where: { id: GwId },
        data: { isActive: true, status: "UPCOMING" },
      });

      // Increment transfers for new gameweek
      await tx.user.updateMany({
        data: { transfersLeft: { increment: 1 } },
      });

      return nextGW;
    });

    return {
      success: true,
      message: `Gameweek ${result.GW} - ${result.season} started successfully`,
      newGW: result.id,
    };
  } catch (error) {
    console.error("Error starting next gameweek:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

export async function startGameweek(
  GwId: string
): Promise<GameweekTransitionResult> {
  try {
    const result = await prisma.$transaction(async (tx) => {
      // Start gameweek
      const nextGW = await tx.gameWeek.update({
        where: { id: GwId },
        data: { status: "LIVE" },
      });

      return nextGW;
    });

    return {
      success: true,
      message: `Gameweek ${result.GW} - ${result.season} started successfully`,
      newGW: result.id,
    };
  } catch (error) {
    console.error("Error starting next gameweek:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}
