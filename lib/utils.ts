import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { PlayerGWStats, Position, Prisma } from "@prisma/client";
import { PlayerInfo } from "@/types/types";

export interface TransferValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface TransferSummary {
  playersIn: PlayerInfo[];
  playersOut: PlayerInfo[];
  transferCount: number;
  penaltyPoints: number;
  newTeamValue: number;
  teamValueChange: number;
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

const positionOrder = { GK: 1, DEF: 2, MID: 3, ATK: 4 };

export function sortPlayers(
  players: PlayerInfo[],
  sortBy: string = "position"
) {
  if (sortBy === "price") {
    return players.sort(
      (a, b) => Number(b.currentPrice) - Number(a.currentPrice)
    );
  }
  return players.sort((a, b) => {
    if (a.position !== b.position) {
      return positionOrder[a.position] - positionOrder[b.position];
    }
    return a.name.localeCompare(b.name);
  });
}

// Team helper functions
export const isStarter = (p: PlayerInfo, starters: PlayerInfo[]): boolean => {
  return starters.some((s) => s.id === p.id);
};

export const isSub = (p: PlayerInfo, subs: PlayerInfo[]): boolean => {
  return subs.some((s) => s.id === p.id);
};

export const isRemoved = (
  player: PlayerInfo,
  removedPlayers: PlayerInfo[]
): boolean => {
  return removedPlayers.some((p) => p.id === player.id);
};

const generatePlayer = (position: Position): PlayerInfo => {
  return {
    id: `fake-${Math.floor(Math.random() * 1000)}`,
    name: "",
    isSuspended: false,
    suspendedUntil: null,
    SuspensionType: null,
    position: position,
    currentPrice: new Prisma.Decimal(0),
    teamId: "",
    team: {
      id: "",
      name: "",
      abbr: "",
      logo: "",
      jersey: "",
    },
    totalStats: null,
    gwStats: [],
  };
};

export function generateFakeBoardingTeam() {
  const starters = Array.from(
    { length: 1 },
    () => generatePlayer("GK") as PlayerInfo
  )
    .concat(
      Array.from({ length: 4 }, () => generatePlayer("DEF") as PlayerInfo)
    )
    .concat(
      Array.from({ length: 3 }, () => generatePlayer("MID") as PlayerInfo)
    )
    .concat(
      Array.from({ length: 3 }, () => generatePlayer("ATK") as PlayerInfo)
    );
  const subs = Array.from(
    { length: 1 },
    () => generatePlayer("GK") as PlayerInfo
  )
    .concat(
      Array.from({ length: 1 }, () => generatePlayer("DEF") as PlayerInfo)
    )
    .concat(
      Array.from({ length: 2 }, () => generatePlayer("MID") as PlayerInfo)
    );
  const removedPlayers = [...starters, ...subs];
  return {
    subs,
    starters,
    removedPlayers,
  };
}

// Transfer validation constants
export const TRANSFER_RULES = {
  MAX_BUDGET: 200,
  MAX_PLAYERS_PER_TEAM: 10,
  TRANSFER_PENALTY_POINTS: -4,
  REQUIRED_STARTERS: 11,
  REQUIRED_SUBS: 4,
  REQUIRED_POSITIONS: { GK: 2, DEF: 5, MID: 5, ATK: 3 },
  STARTER_MIN_NUM: 11,
  SUB_MIN_NUM: 4,
} as const;

// Team validation constants
export const TEAM_RULES = {
  PLAYERS: 15,
  REQUIRED_STARTERS: 11,
  REQUIRED_SUBS: 4,
  MIN_STARTERS: { GK: 1, DEF: 3, MID: 3, ATK: 1 },
  MAX_STARTERS: { GK: 1, DEF: 5, MID: 5, ATK: 3 },
} as const;

// Points calculation constants
export const POINTS_RULES = {
  MINUTES_PLAYED: 1,
  MINUTES_THRESHOLD: 60,
  GOALS_SCORED: {
    GK: 6,
    DEF: 6,
    MID: 5,
    ATK: 4,
  },
  ASSISTS: 3,
  CLEAN_SHEETS: {
    GK: 4,
    DEF: 4,
    MID: 1,
    ATK: 0,
  },
  SAVES_Threshold: 3,
  PENALTIES_SAVED: 3,
  PENALTIES_MISSED: -2,
  YELLOW_CARDS: -1,
  RED_CARDS: -2,
  OWN_GOALS: -2,
  CAPTAIN_MULTIPLIER: 2,
  POTM_BONUS: 3,
} as const;

export const calculatePlayerPoints = (
  stats: PlayerGWStats,
  position: Position,
  isCaptain: boolean = false
) => {
  console.log(stats, position)
  let points = 0;

  // Basic points
  points += stats.goalsScored * POINTS_RULES.GOALS_SCORED[position];
  points += stats.assists * POINTS_RULES.ASSISTS;
  points += stats.penaltiesSaved * POINTS_RULES.PENALTIES_SAVED;
  points += stats.penaltiesMissed * POINTS_RULES.PENALTIES_MISSED;
  points += stats.yellowCards * POINTS_RULES.YELLOW_CARDS;
  points += stats.redCards * POINTS_RULES.RED_CARDS;
  points += stats.ownGoals * POINTS_RULES.OWN_GOALS;

  // Minutes played
  if (stats.minutesPlayed > 0) points += POINTS_RULES.MINUTES_PLAYED;
  if (stats.minutesPlayed >= POINTS_RULES.MINUTES_THRESHOLD)
    points += POINTS_RULES.MINUTES_PLAYED;
  if (stats.saves >= POINTS_RULES.SAVES_Threshold)
    points += Math.trunc(stats.saves / POINTS_RULES.SAVES_Threshold);
  if (stats.cleanSheets === 1) points += POINTS_RULES.CLEAN_SHEETS[position];
  if (stats.potm) points += POINTS_RULES.POTM_BONUS;

  // Captain multiplier
  if (isCaptain) points *= 2;

  return points;
};

/**
 * Validates if a team meets all transfer rules
 */
export function validateTeamTransfers(
  starters: PlayerInfo[],
  subs: PlayerInfo[]
): TransferValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  const team = [...starters, ...subs];

  // Rule 0: Check for player numbers
  if (TRANSFER_RULES.STARTER_MIN_NUM !== starters.length) {
    errors.push(
      `Team must have exactly ${TRANSFER_RULES.STARTER_MIN_NUM} starters (currently ${starters.length})`
    );
  }
  if (TRANSFER_RULES.SUB_MIN_NUM !== subs.length) {
    errors.push(
      `Team must have exactly ${TRANSFER_RULES.SUB_MIN_NUM} substitutes (currently ${subs.length})`
    );
  }

  // Rule 1: Check team size
  if (starters.length !== TRANSFER_RULES.REQUIRED_STARTERS) {
    errors.push(
      `Team must have exactly ${TRANSFER_RULES.REQUIRED_STARTERS} starters (currently ${starters.length})`
    );
  }

  if (subs.length !== TRANSFER_RULES.REQUIRED_SUBS) {
    errors.push(
      `Team must have exactly ${TRANSFER_RULES.REQUIRED_SUBS} substitutes (currently ${subs.length})`
    );
  }

  // Rule 2: Check budget constraint
  const totalValue = team.reduce(
    (sum, player) => sum + Number(player.currentPrice),
    0
  );
  if (totalValue > TRANSFER_RULES.MAX_BUDGET) {
    errors.push(
      `Team value (${totalValue}M) exceeds budget limit of ${TRANSFER_RULES.MAX_BUDGET}M`
    );
  }

  // Rule 3: Check max players per team constraint
  const teamCounts = team.reduce((counts, player) => {
    counts[player.teamId] = (counts[player.teamId] || 0) + 1;
    return counts;
  }, {} as Record<string, number>);

  Object.entries(teamCounts).forEach(([teamId, count]) => {
    if (count > TRANSFER_RULES.MAX_PLAYERS_PER_TEAM) {
      errors.push(
        `Too many players from ${teamId} (${count}/${TRANSFER_RULES.MAX_PLAYERS_PER_TEAM} max)`
      );
    }
  });

  // Rule 4: Check position requirements
  const positionCounts = team.reduce((counts, player) => {
    counts[player.position] = (counts[player.position] || 0) + 1;
    return counts;
  }, {} as Record<Position, number>);

  // Required positions: 2 GK, 5 DEF, 5 MID, 3 ATK
  const requiredPositions = { GK: 2, DEF: 5, MID: 5, ATK: 3 };
  Object.entries(requiredPositions).forEach(([position, required]) => {
    const current = positionCounts[position as Position] || 0;
    if (current !== required) {
      errors.push(`Invalid ${position} count: ${current}/${required} required`);
    }
  });

  // Add warnings for high team value
  if (totalValue > TRANSFER_RULES.MAX_BUDGET * 0.9) {
    warnings.push(
      `Team value is ${((totalValue / TRANSFER_RULES.MAX_BUDGET) * 100).toFixed(
        1
      )}% of budget`
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

export function validateTeamComposition(
  starters: PlayerInfo[],
  subs: PlayerInfo[]
): TransferValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  const team = [...starters, ...subs];

  // Rule 0: Check for player numbers
  if (TEAM_RULES.REQUIRED_STARTERS !== starters.length) {
    errors.push(
      `Team must have exactly ${TEAM_RULES.REQUIRED_STARTERS} starters (currently ${starters.length})`
    );
  }
  if (TEAM_RULES.REQUIRED_SUBS !== subs.length) {
    errors.push(
      `Team must have exactly ${TEAM_RULES.REQUIRED_SUBS} substitutes (currently ${subs.length})`
    );
  }

  // Rule 1: Check team size
  if (team.length !== TEAM_RULES.PLAYERS) {
    errors.push(
      `Team must have exactly ${TEAM_RULES.PLAYERS} players (currently ${team.length})`
    );
  }

  const positionCounts = starters.reduce((counts, player) => {
    counts[player.position] = (counts[player.position] || 0) + 1;
    return counts;
  }, {} as Record<Position, number>);

  // Rule 2: Check min starters per position
  Object.entries(TEAM_RULES.MIN_STARTERS).forEach(([position, min]) => {
    const current = positionCounts[position as Position] || 0;
    if (current < min) {
      errors.push(`Not enough ${position} starters: ${current}/${min} min`);
    }
  });

  // Rule 3: Check max starters per position
  Object.entries(TEAM_RULES.MAX_STARTERS).forEach(([position, max]) => {
    const current = positionCounts[position as Position] || 0;
    if (current > max) {
      errors.push(`Too many ${position} starters: ${current}/${max} max`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

export function getSubsList(
  p: PlayerInfo | null,
  subs: PlayerInfo[],
  starters: PlayerInfo[]
) {
  const positionCounts = starters.reduce((counts, player) => {
    counts[player.position] = (counts[player.position] || 0) + 1;
    return counts;
  }, {} as Record<Position, number>);
  console.log("positionCounts", positionCounts);
  if (!p) return [];
  if (p.position === "GK") {
    return subs.filter((sub) => sub.position === "GK");
  } else if (
    p.position === "DEF" &&
    positionCounts.DEF === TEAM_RULES.MIN_STARTERS.DEF
  ) {
    return subs.filter((sub) => sub.position === "DEF");
  } else if (
    p.position === "MID" &&
    positionCounts.MID === TEAM_RULES.MIN_STARTERS.MID
  ) {
    return subs.filter((sub) => sub.position === "MID");
  } else if (
    p.position === "ATK" &&
    positionCounts.ATK === TEAM_RULES.MIN_STARTERS.ATK
  ) {
    return subs.filter((sub) => sub.position === "ATK");
  } else {
    return subs.filter((sub) => sub.position !== "GK");
  }
}

/**
 * Calculates transfer summary including penalties
 */
export function calculateTransferSummary(
  currentStarters: PlayerInfo[],
  currentSubs: PlayerInfo[],
  originalStarters: PlayerInfo[],
  originalSubs: PlayerInfo[]
): TransferSummary {
  const currentTeam = [...currentStarters, ...currentSubs];
  const originalTeam = [...originalStarters, ...originalSubs];

  // Find players transferred in and out
  const originalPlayerIds = new Set(originalTeam.map((p) => p.id));
  const currentPlayerIds = new Set(currentTeam.map((p) => p.id));

  const playersIn = currentTeam.filter((p) => !originalPlayerIds.has(p.id));
  const playersOut = originalTeam.filter((p) => !currentPlayerIds.has(p.id));

  const transferCount = playersIn.length; // Should equal playersOut.length
  const penaltyPoints = transferCount * TRANSFER_RULES.TRANSFER_PENALTY_POINTS;

  const originalTeamValue = originalTeam.reduce(
    (sum, p) => sum + Number(p.currentPrice),
    0
  );
  const newTeamValue = currentTeam.reduce(
    (sum, p) => sum + Number(p.currentPrice),
    0
  );
  const teamValueChange = newTeamValue - originalTeamValue;

  return {
    playersIn,
    playersOut,
    transferCount,
    penaltyPoints,
    newTeamValue,
    teamValueChange,
  };
}

const getPlayerPositionShipColor = (position: Position) => {
  switch (position) {
    case "GK":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";
    case "DEF":
      return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
    case "MID":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300";
    case "ATK":
      return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";
  }
};

export function getPlayerPositionColor(position: Position) {
  return getPlayerPositionShipColor(position);
}
