import { Player, Position, PrismaClient } from "@prisma/client";
import { main as readCSVFile } from "./read-csv";

const prisma = new PrismaClient();

async function restoreDatabase() {
  try {
    console.log("🚀 Starting database restoration...");
    const { teams, players, fixtures_2025 } = await readCSVFile();
    console.log("Teams:", teams);
    console.log("Players:", players);

    
    // Clear existing data if any (in reverse order of dependencies)
    console.log("🗑️  Clearing existing data...");
    await prisma.playerGWStats.deleteMany();
    await prisma.userGWSheet.deleteMany();
    await prisma.player.deleteMany();
    await prisma.fixture.deleteMany();
    await prisma.gameWeek.deleteMany();
    await prisma.team.deleteMany();
    console.log("✅ Existing data cleared");

    // Create GameWeeks for 2025 season
    console.log("📅 Creating GameWeeks...");
    const gameweeks = [];
    for (let i = 1; i <= 30; i++) {
      const gameweek = await prisma.gameWeek.create({
        data: {
          season: "2025",
          GW: i,
          isActive: i === 1,
        },
      });
      gameweeks.push(gameweek);
    }
    console.log(`✅ Created ${gameweeks.length} GameWeeks`);

    // Set active gameweek to 1
    console.log(`✅ Set active gameweek to ${gameweeks[0].id}`);

    // Insert teams
    console.log("🏟️  Inserting teams...");
    for (const team of teams) {
      await prisma.team.create({
        data: {
          id: team.id,
          name: team.name,
          abbr: team.abbr,
          logo: team.logo,
          jersey: team.jersey,
        },
      });
    }
    console.log(`✅ Inserted ${teams.length} teams`);

    //Insert 2025 fixtures
    console.log("fixtures:", fixtures_2025);
    for (const fixture of fixtures_2025) {
      await prisma.fixture.create({
        data: {
          teamHId: fixture.teamHId,
          teamAId: fixture.teamAId,
          GW: gameweeks.find((gw) => gw.GW.toString() === fixture.GW && gw.season === "2025" )?.id || "",
          kickoffTime: new Date(fixture.kickoffTime),
        },
      });
    }

    console.log("fixtures inserted");

    // Insert players (batch 1)
    for (const playersBatch of Object.values(players)) {
      for (const player of playersBatch as Player[]) {
        await prisma.player.create({
          data: {
            id: player.id,
            name: player.name,
            teamId: player.teamId,
            currentPrice: player.currentPrice,
            position: player.position as Position,
          },
        });
        console.log(`   - Inserted player ${player.name}`);
        // creating player stats for each gameweek
        for (const gameweek of gameweeks) {
          await prisma.playerGWStats.create({
            data: {
              playerId: player.id,
              GW: gameweek.id,
              points: 0,
              minutesPlayed: 0,
              goalsScored: 0,
              assists: 0,
              cleanSheets: 0,
              goalsConceded: 0,
              ownGoals: 0,
              penaltiesSaved: 0,
              penaltiesMissed: 0,
              yellowCards: 0,
              redCards: 0,
              saves: 0,
              bonus: 0,
              bps: 0,
              price: player.currentPrice ? Number(player.currentPrice) : 0,
              transfersIn: 0,
              transfersOut: 0,
              owners: 0,
            },
          });
        }
        console.log(`   - Inserted stats for player ${player.name}`);
      }
    }

    // console.log(`✅ Inserted ${players.length} players`);    

    console.log("🎉 Database restoration completed successfully!");
    console.log("📊 Summary:");
    console.log(`   - GameWeeks: ${gameweeks.length}`);
  } catch (error) {
    console.error("❌ Error during database restoration:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the restoration
restoreDatabase()
  .then(() => {
    console.log("✅ Database restoration script completed");
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Database restoration script failed:", error);
    process.exit(1);
  });
