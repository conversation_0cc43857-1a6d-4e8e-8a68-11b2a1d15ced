import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// GET /api/admin/teams/[id] - Get single team with details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side

    const { id } = await params;

    const team = await prisma.team.findUnique({
      where: { id },
      include: {
        players: {
          select: {
            id: true,
            name: true,
            position: true,
            currentPrice: true,
            totalStats: {
              select: {
                points: true,
                goalsScored: true,
                assists: true,
                cleanSheets: true,
              },
            },
          },
          orderBy: [
            { position: "asc" },
            { name: "asc" },
          ],
        },
        homeFixtures: {
          select: {
            id: true,
            GW: true,
            kickoffTime: true,
            teamHScore: true,
            teamAScore: true,
            teamA: {
              select: {
                id: true,
                name: true,
                abbr: true,
                logo: true,
              },
            },
          },
          orderBy: { kickoffTime: "desc" },
          take: 5,
        },
        awayFixtures: {
          select: {
            id: true,
            GW: true,
            kickoffTime: true,
            teamHScore: true,
            teamAScore: true,
            teamH: {
              select: {
                id: true,
                name: true,
                abbr: true,
                logo: true,
              },
            },
          },
          orderBy: { kickoffTime: "desc" },
          take: 5,
        },
        _count: {
          select: {
            players: true,
            homeFixtures: true,
            awayFixtures: true,
          },
        },
      },
    });

    if (!team) {
      return NextResponse.json({ error: "Team not found" }, { status: 404 });
    }

    return NextResponse.json(team);
  } catch (error) {
    console.error("Error fetching team:", error);
    return NextResponse.json(
      { error: "Failed to fetch team" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/teams/[id] - Update team
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side

    const { id } = await params;
    const body = await request.json();
    const { name, abbr, logo, jersey } = body;

    // Validate required fields
    if (!name || !abbr) {
      return NextResponse.json(
        { error: "Name and abbreviation are required" },
        { status: 400 }
      );
    }

    // Check if team exists
    const existingTeam = await prisma.team.findUnique({
      where: { id },
    });

    if (!existingTeam) {
      return NextResponse.json({ error: "Team not found" }, { status: 404 });
    }

    // Check if another team with same name or abbreviation exists
    const duplicateTeam = await prisma.team.findFirst({
      where: {
        AND: [
          { id: { not: id } },
          {
            OR: [
              { name: { equals: name, mode: "insensitive" } },
              { abbr: { equals: abbr, mode: "insensitive" } },
            ],
          },
        ],
      },
    });

    if (duplicateTeam) {
      return NextResponse.json(
        { error: "Another team with this name or abbreviation already exists" },
        { status: 400 }
      );
    }

    const updatedTeam = await prisma.team.update({
      where: { id },
      data: {
        name,
        abbr: abbr.toUpperCase(),
        logo: logo || existingTeam.logo,
        jersey: jersey || existingTeam.jersey,
      },
      include: {
        players: {
          select: {
            id: true,
            name: true,
            position: true,
            currentPrice: true,
          },
        },
        _count: {
          select: {
            players: true,
            homeFixtures: true,
            awayFixtures: true,
          },
        },
      },
    });

    return NextResponse.json(updatedTeam);
  } catch (error) {
    console.error("Error updating team:", error);
    return NextResponse.json(
      { error: "Failed to update team" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/teams/[id] - Delete team
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side

    const { id } = await params;

    // Check if team exists
    const team = await prisma.team.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            players: true,
            homeFixtures: true,
            awayFixtures: true,
          },
        },
      },
    });

    if (!team) {
      return NextResponse.json({ error: "Team not found" }, { status: 404 });
    }

    // Check if team has players or fixtures
    if (team._count.players > 0) {
      return NextResponse.json(
        { error: "Cannot delete team with players. Please reassign or delete players first." },
        { status: 400 }
      );
    }

    if (team._count.homeFixtures > 0 || team._count.awayFixtures > 0) {
      return NextResponse.json(
        { error: "Cannot delete team with fixtures. Please delete fixtures first." },
        { status: 400 }
      );
    }

    await prisma.team.delete({
      where: { id },
    });

    return NextResponse.json({ message: "Team deleted successfully" });
  } catch (error) {
    console.error("Error deleting team:", error);
    return NextResponse.json(
      { error: "Failed to delete team" },
      { status: 500 }
    );
  }
}
