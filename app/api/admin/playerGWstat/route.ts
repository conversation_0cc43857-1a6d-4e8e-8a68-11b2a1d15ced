import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const playerId = searchParams.get("playerId");
    const GW = searchParams.get("GW");

    if (!playerId || !GW) {
      return NextResponse.json(
        { error: "Missing required fields: playerId, GW" },
        { status: 400 }
      );
    }

    const stats = await prisma.playerGWStats.findUnique({
      where: {
        playerId_GW: {
          playerId,
          GW: GW, // GW is now the GameWeek ID
        },
      },
    });

    if (!stats) {
      return NextResponse.json(
        { error: "Stats not found for this player and gameweek" },
        { status: 404 }
      );
    }

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error fetching player stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const {
      playerId,
      GW,
      price,
      points,
      transfersIn,
      transfersOut,
      owners,
      minutesPlayed,
      goalsScored,
      assists,
      cleanSheets,
      goalsConceded,
      ownGoals,
      penaltiesSaved,
      penaltiesMissed,
      yellowCards,
      redCards,
      saves,
      bonus,
      bps,
      potm = false,
      wasSubstituted = false,
    } = body;

    // Validate required fields (GW is now GameWeek ID)
    if (playerId === undefined || GW === undefined) {
      return NextResponse.json(
        { error: "Missing required fields: playerId, GW (GameWeek ID)" },
        { status: 400 }
      );
    }

    // Check if stats already exist for this GW
    const existingStats = await prisma.playerGWStats.findUnique({
      where: {
        playerId_GW: {
          playerId,
          GW: GW, // GW is now the GameWeek ID
        },
      },
    });

    if (existingStats) {
      return NextResponse.json(
        { error: "Stats already exist for this gameweek. Use PUT to update." },
        { status: 409 }
      );
    }

    const newStats = await prisma.playerGWStats.create({
      data: {
        playerId,
        GW: GW, // GW is now the GameWeek ID
        price: parseInt(price),
        points: parseInt(points) || 0,
        transfersIn: parseInt(transfersIn) || 0,
        transfersOut: parseInt(transfersOut) || 0,
        owners: parseInt(owners) || 0,
        minutesPlayed: parseInt(minutesPlayed) || 0,
        goalsScored: parseInt(goalsScored) || 0,
        assists: parseInt(assists) || 0,
        cleanSheets: parseInt(cleanSheets) || 0,
        goalsConceded: parseInt(goalsConceded) || 0,
        ownGoals: parseInt(ownGoals) || 0,
        penaltiesSaved: parseInt(penaltiesSaved) || 0,
        penaltiesMissed: parseInt(penaltiesMissed) || 0,
        yellowCards: parseInt(yellowCards) || 0,
        redCards: parseInt(redCards) || 0,
        saves: parseInt(saves) || 0,
        potm,
        wasSubstituted,
        bonus: parseInt(bonus) || 0,
        bps: parseInt(bps) || 0,
      },
    });

    return NextResponse.json(newStats, { status: 201 });
  } catch (error) {
    console.error("Error creating player stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(req: Request) {
  try {
    const body = await req.json();
    const {
      playerId,
      GW,
      price,
      points,
      transfersIn,
      transfersOut,
      owners,
      minutesPlayed,
      goalsScored,
      assists,
      cleanSheets,
      goalsConceded,
      ownGoals,
      penaltiesSaved,
      penaltiesMissed,
      yellowCards,
      redCards,
      saves,
      bonus,
      bps,
      potm = false,
      wasSubstituted = false,
    } = body;

    // Validate required fields (GW is now GameWeek ID)
    if (playerId === undefined || GW === undefined) {
      return NextResponse.json(
        { error: "Missing required fields: playerId, GW (GameWeek ID)" },
        { status: 400 }
      );
    }

    // Check if stats exist
    const existingStats = await prisma.playerGWStats.findUnique({
      where: {
        playerId_GW: {
          playerId,
          GW,
        },
      },
    });

    if (!existingStats) {
      return NextResponse.json(
        { error: "Stats not found for this player and gameweek" },
        { status: 404 }
      );
    }

    const updatedStats = await prisma.playerGWStats.update({
      where: {
        playerId_GW: {
          playerId,
          GW: GW,
        },
      },
      data: {
        price: parseInt(price),
        points: parseInt(points) || 0,
        transfersIn: parseInt(transfersIn) || 0,
        transfersOut: parseInt(transfersOut) || 0,
        owners: parseInt(owners) || 0,
        minutesPlayed: parseInt(minutesPlayed) || 0,
        goalsScored: parseInt(goalsScored) || 0,
        assists: parseInt(assists) || 0,
        cleanSheets: parseInt(cleanSheets) || 0,
        goalsConceded: parseInt(goalsConceded) || 0,
        ownGoals: parseInt(ownGoals) || 0,
        penaltiesSaved: parseInt(penaltiesSaved) || 0,
        penaltiesMissed: parseInt(penaltiesMissed) || 0,
        yellowCards: parseInt(yellowCards) || 0,
        redCards: parseInt(redCards) || 0,
        saves: parseInt(saves) || 0,
        bonus: parseInt(bonus) || 0,
        bps: parseInt(bps) || 0,
        potm,
        wasSubstituted,
      },
    });

    return NextResponse.json(updatedStats, { status: 200 });
  } catch (error) {
    console.error("Error updating player stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const playerId = searchParams.get("playerId");
    const GW = searchParams.get("GW");

    if (!playerId || !GW) {
      return NextResponse.json(
        { error: "Missing required fields: playerId, GW" },
        { status: 400 }
      );
    }

    await prisma.playerGWStats.delete({
      where: {
        playerId_GW: {
          playerId,
          GW: GW, // GW is now the GameWeek ID
        },
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting player stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
