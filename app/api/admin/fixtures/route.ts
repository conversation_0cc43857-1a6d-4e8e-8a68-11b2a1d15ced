import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// GET /api/admin/fixtures - Get all fixtures with filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const gameWeek = searchParams.get("gameWeek");
    const teamId = searchParams.get("teamId");
    const status = searchParams.get("status");
    const limit = searchParams.get("limit");

    // Build where clause
    const where: import("@prisma/client").Prisma.FixtureWhereInput = {};

    if (gameWeek && gameWeek !== "all") {
      where.GW = gameWeek;
    }

    if (teamId && teamId !== "all") {
      where.OR = [
        { teamHId: teamId },
        { teamAId: teamId },
      ];
    }

    if (status && status !== "all") {
      const now = new Date();
      if (status === "completed") {
        where.AND = [
          { teamHScore: { not: null } },
          { teamAScore: { not: null } },
        ];
      } else if (status === "live") {
        where.AND = [
          { kickoffTime: { lt: now } },
          {
            OR: [
              { teamHScore: null },
              { teamAScore: null },
            ],
          },
        ];
      } else if (status === "scheduled") {
        where.kickoffTime = { gt: now };
      }
    }

    const fixtures = await prisma.fixture.findMany({
      where,
      include: {
        teamH: {
          select: {
            id: true,
            name: true,
            abbr: true,
            logo: true,
          },
        },
        teamA: {
          select: {
            id: true,
            name: true,
            abbr: true,
            logo: true,
          },
        },
        stats: {
          select: {
            id: true,
            identifier: true,
            h: true,
            a: true,
          },
        },
      },
      orderBy: [
        { GW: "asc" },
        { kickoffTime: "asc" },
      ],
      take: limit ? parseInt(limit) : undefined,
    });

    return NextResponse.json(fixtures);
  } catch (error) {
    console.error("Error fetching fixtures:", error);
    return NextResponse.json(
      { error: "Failed to fetch fixtures" },
      { status: 500 }
    );
  }
}

// POST /api/admin/fixtures - Create new fixture
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { GW, kickoffTime, teamHId, teamAId } = body;

    // Validate required fields
    if (!GW || !kickoffTime || !teamHId || !teamAId) {
      return NextResponse.json(
        { error: "Round, kickoff time, and both teams are required" },
        { status: 400 }
      );
    }

    // Validate teams are different
    if (teamHId === teamAId) {
      return NextResponse.json(
        { error: "Home and away teams must be different" },
        { status: 400 }
      );
    }

    // Check if teams exist
    const [teamH, teamA] = await Promise.all([
      prisma.team.findUnique({ where: { id: teamHId } }),
      prisma.team.findUnique({ where: { id: teamAId } }),
    ]);

    if (!teamH || !teamA) {
      return NextResponse.json(
        { error: "One or both teams not found" },
        { status: 400 }
      );
    }

    // Check for duplicate fixture
    const existingFixture = await prisma.fixture.findFirst({
      where: {
        GW,
        teamHId,
        teamAId,
      },
    });

    if (existingFixture) {
      return NextResponse.json(
        { error: "Fixture between these teams in this round already exists" },
        { status: 400 }
      );
    }

    // Create fixture 
    const fixture = await prisma.fixture.create({
      data: {
        GW,
        kickoffTime: new Date(kickoffTime),
        teamHId,
        teamAId,
      },
      include: {
        teamH: {
          select: {
            id: true,
            name: true,
            abbr: true,
            logo: true,
          },
        },
        teamA: {
          select: {
            id: true,
            name: true,
            abbr: true,
            logo: true,
          },
        },
        stats: {
          select: {
            id: true,
            identifier: true,
            h: true,
            a: true,
          },
        },
      },
    });

    return NextResponse.json(fixture, { status: 201 });
  } catch (error) {
    console.error("Error creating fixture:", error);
    return NextResponse.json(
      { error: "Failed to create fixture" },
      { status: 500 }
    );
  }
}
