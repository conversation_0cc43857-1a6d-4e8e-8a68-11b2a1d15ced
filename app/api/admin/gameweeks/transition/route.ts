// app/api/admin/gameweeks/transition/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { activateGameweek, finishGameweek, startGameweek } from '@/lib/gameweek-service';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, GwId, } = body;

    // Basic validation
    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    let result;

    switch (action) {
      case 'finish':
        if (!GwId) {
          return NextResponse.json(
            { error: 'currentGWId is required for finish action' },
            { status: 400 }
          );
        }
        result = await finishGameweek(GwId);
        break;
        case 'activate':
        if (!GwId) {
          return NextResponse.json(
            { error: 'nextGWId is required for activate action' },
            { status: 400 }
          );
        }
        result = await activateGameweek(GwId);
        break;
      case 'start':
        if (!GwId) {
          return NextResponse.json(
            { error: 'season and nextGWNumber are required for start action' },
            { status: 400 }
          );
        }
        result = await startGameweek(GwId);
        break;


      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: finish, start, or transition' },
          { status: 400 }
        );
    }

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });
    } else {
      return NextResponse.json(
        { error: result.message },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in gameweek transition:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

