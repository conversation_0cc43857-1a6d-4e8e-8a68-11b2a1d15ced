
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const activeGameweek = await prisma.gameWeek.findFirst({
      where: {
        isActive: true,
      },
    });

    if (!activeGameweek) {
      return NextResponse.json(
        { error: "No active gameweek found" },
        { status: 404 }
      );
    }

    return NextResponse.json(activeGameweek);
  } catch (error) {
    console.error("Error fetching active gameweek:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(req: Request) {
  try {
    const body = await req.json();
    const { id, isActive } = body;
    const activeGameweek = await prisma.gameWeek.update({
      where: { id },
      data: {
        isActive,
      },
    });

    return NextResponse.json(activeGameweek);
  } catch (error) {
    console.error("Error updating active gameweek:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
