import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const [
      totalTeams,
      totalPlayers,
      totalFixtures,
      completedFixtures,
      upcomingFixtures,
      liveFixtures,
    ] = await Promise.all([
      prisma.team.count(),
      prisma.player.count(),
      prisma.fixture.count(),
      prisma.fixture.count({
        where: {
          AND: [
            { teamHScore: { not: null } },
            { teamAScore: { not: null } },
          ],
        },
      }),
      prisma.fixture.count({
        where: {
          kickoffTime: { gt: new Date() },
        },
      }),
      prisma.fixture.count({
        where: {
          AND: [
            { kickoffTime: { lt: new Date() } },
            {
              OR: [
                { teamHScore: null },
                { teamAScore: null },
              ],
            },
          ],
        },
      }),
    ]);

    // Get recent fixtures for the dashboard
    const recentFixtures = await prisma.fixture.findMany({
      take: 5,
      orderBy: { kickoffTime: "desc" },
      include: {
        teamH: {
          select: {
            name: true,
            abbr: true,
            logo: true,
          },
        },
        teamA: {
          select: {
            name: true,
            abbr: true,
            logo: true,
          },
        },
      },
    });

    return NextResponse.json({
      totalTeams,
      totalPlayers,
      totalFixtures,
      completedFixtures,
      upcomingFixtures,
      liveFixtures,
      recentFixtures,
    });
  } catch (error) {
    console.error("Error fetching admin stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
