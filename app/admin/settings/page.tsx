"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Database,
  Refresh<PERSON>w,
  Alert<PERSON><PERSON>gle,
  CheckCircle2,
  Clock,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { GameWeek } from "@prisma/client";
import { Status } from "@/types/types";

export default function AdminSettingsPage() {
  const [gameweeks, setGameweeks] = useState<GameWeek[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Fetch all gameweeks
      const gameweeksResponse = await fetch("/api/admin/gameweeks");
      const gameweeksData = await gameweeksResponse.json();
      setGameweeks(gameweeksData);
    } catch (error) {
      console.error("Error fetching data:", error);
      setMessage({ type: "error", text: "Failed to load settings data" });
    } finally {
      setLoading(false);
    }
  };



  const handleGameweekTransition = async (action: string, gwId: string) => {
    try {
      setSaving(true);

      const response = await fetch("/api/admin/gameweeks/transition", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action, GwId: gwId }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setMessage({ type: "success", text: result.message });
        fetchData(); // Refresh data
      } else {
        throw new Error(result.message || "Failed to update gameweek");
      }
    } catch (error) {
      console.error("Error updating gameweek:", error);
      setMessage({
        type: "error",
        text: error instanceof Error ? error.message : "Failed to update gameweek"
      });
    } finally {
      setSaving(false);
    }
  };

  const getStatusBadgeVariant = (status: Status | null) => {
    switch (status) {
      case "UPCOMING":
        return "secondary";
      case "LIVE":
        return "default";
      case "FINISHED":
        return "outline";
      default:
        return "secondary";
    }
  };

  const getStatusIcon = (status: Status | null) => {
    switch (status) {
      case "UPCOMING":
        return <Clock className="h-4 w-4" />;
      case "LIVE":
        return <RefreshCw className="h-4 w-4" />;
      case "FINISHED":
        return <CheckCircle2 className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const currentSeasonGameweeks = gameweeks.filter(
    (gw) => gw.season === "2025"
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Settings</h1>
          <p className="text-muted-foreground">
            Manage system settings and configurations
          </p>
        </div>
      </div>

      {message?.type === "error" && (
        <Alert className={message.type === "error" ? "border-destructive" : "border-green-500"}>
          {message.type === "error" ? (
            <AlertTriangle className="h-4 w-4" />
          ) : (
            <CheckCircle2 className="h-4 w-4" />
          )}
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* Gameweeks Management Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Gameweeks Management
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Gameweek</TableHead>
                <TableHead>Season</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Active</TableHead>
                <TableHead>Deadline</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentSeasonGameweeks.map((gw) => (
                <TableRow key={gw.id}>
                  <TableCell className="font-medium">GW {gw.GW}</TableCell>
                  <TableCell>{gw.season}</TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(gw.status)}>
                      {getStatusIcon(gw.status)}
                      {gw.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {gw.isActive ? (
                      <Badge variant="default">Active</Badge>
                    ) : (
                      <Badge variant="outline">Inactive</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {gw.deadline ? new Date(gw.deadline).toLocaleDateString() : "Not set"}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      {gw.status === "UPCOMING" && gw.isActive && (
                        <Button
                          size="sm"
                          variant="default"
                          onClick={() => handleGameweekTransition("start", gw.id)}
                          disabled={saving}
                        >
                          Start
                        </Button>
                      )}
                      {gw.status === "LIVE" && (
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleGameweekTransition("finish", gw.id)}
                          disabled={saving}
                        >
                          Finish
                        </Button>
                      )}
                      {gw.status === "FINISHED" && (
                        <Badge variant="outline">Completed</Badge>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}