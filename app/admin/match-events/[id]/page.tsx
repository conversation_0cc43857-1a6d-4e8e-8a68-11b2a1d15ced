"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  Save,
  Users,
  Clock,
  Plus,
  Minus,
  Calendar,
  Trophy,
  Activity,
} from "lucide-react";
import Link from "next/link";
import { calculatePlayerPoints, getPlayerPositionColor } from "@/lib/utils";
import { PlayerGWStats, Position } from "@prisma/client";
import { FixtureInfo, PlayerInfo } from "@/types/types";

interface PlayerMatchData extends PlayerInfo {
  playerId: string;
  position: Position;
  minutesPlayed: number;
  goalsScored: number;
  assists: number;
  yellowCards: number;
  redCards: number;
  cleanSheets: number;
  goalsConceded: number;
  saves: number;
  penaltiesSaved: number;
  penaltiesMissed: number;
  ownGoals: number;
  points: number;
  bonus: number;
  bps: number;
  wasSubstituted: boolean;
  potm: boolean;
  GW: string;
}

export default function MatchInputPage() {
  const params = useParams();
  const router = useRouter();
  const fixtureId = params.id as string;

  const [fixture, setFixture] = useState<FixtureInfo | null>(null);
  const [homeTeamPlayers, setHomeTeamPlayers] = useState<PlayerInfo[]>([]);
  const [awayTeamPlayers, setAwayTeamPlayers] = useState<PlayerInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [gameweekId, setGameweekId] = useState<string | null>(null);

  // Match result
  const [homeScore, setHomeScore] = useState<number>(0);
  const [awayScore, setAwayScore] = useState<number>(0);
  const [allPlayers, setAllPlayers] = useState<PlayerInfo[]>([]);
  const [playerData, setPlayerData] = useState<PlayerMatchData[]>([]);
  const [DefaultPlayerData, setDefaultPlayerData] = useState<PlayerMatchData[]>(
    []
  );

  useEffect(() => {
    const fetchFixtureData = async () => {
      try {
        // Fetch fixture details
        const fixtureResponse = await fetch(`/api/admin/fixtures/${fixtureId}`);
        const fixtureData = await fixtureResponse.json();
        setFixture(fixtureData);

        // Set existing scores if available
        if (fixtureData.teamHScore !== null)
          setHomeScore(fixtureData.teamHScore);
        if (fixtureData.teamAScore !== null)
          setAwayScore(fixtureData.teamAScore);

        // Fetch players for both teams
        const [homePlayersResponse, awayPlayersResponse] = await Promise.all([
          fetch(`/api/admin/players?teamId=${fixtureData.teamHId}`),
          fetch(`/api/admin/players?teamId=${fixtureData.teamAId}`),
        ]);

        const homePlayers = await homePlayersResponse.json();
        const awayPlayers = await awayPlayersResponse.json();

        setHomeTeamPlayers(homePlayers);
        setAwayTeamPlayers(awayPlayers);

        // First, find the GameWeek ID for this round
        const gameweekResponse = await fetch(
          `/api/admin/gameweeks?season=2025&GW=${fixtureData.GW}`
        );
        let foundGameweekId = null;
        if (gameweekResponse.ok) {
          const gameweeks = await gameweekResponse.json();
          const gameweek = gameweeks.find(
            (gw: { GW: number; id: string }) => gw.GW === fixtureData.GW
          );
          foundGameweekId = gameweek?.id;
        }

        if (!foundGameweekId) {
          console.error(`GameWeek not found for round ${fixtureData.GW}`);
          return;
        }

        setGameweekId(foundGameweekId);

        // Initialize player data from existing GW stats if available
        const allPlayers = [...homePlayers, ...awayPlayers];
        setAllPlayers(allPlayers);
        const playerData = allPlayers.map((player) => {
          const existingStats = player.gwStats?.find(
            (stat: PlayerGWStats) => stat.GW === foundGameweekId
          );
          return {
            ...player,
            playerId: player.id,
            ...existingStats,
          };
        });
        setPlayerData(playerData);
        setDefaultPlayerData(playerData);
      } catch (error) {
        console.error("Error fetching fixture data:", error);
      } finally {
        setLoading(false);
      }
    };
    if (fixtureId) {
      fetchFixtureData();
    }
  }, [fixtureId]);

  const updatePlayerData = (
    playerId: string,
    field: keyof PlayerMatchData,
    value: number
  ) => {
    setPlayerData((prev) =>
      prev.map((player) => {
        if (player.playerId === playerId) {
          return {
            ...player,
            [field]: Math.max(0, value), // Ensure non-negative values
          };
        }
        return player;
      })
    );
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // Update fixture score
      await fetch(`/api/admin/fixtures/${fixtureId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          kickoffTime: fixture?.kickoffTime,
          teamHId: fixture?.teamHId,
          teamAId: fixture?.teamAId,
          GW: fixture?.GW,
          teamHScore: homeScore,
          teamAScore: awayScore,
        }),
      });

      // Update player stats for each player using PUT method
      const savePromises = Object.values(playerData).map(async (player) => {
        const calculatedPoints = calculatePlayerPoints(player, player.position);

        // Use PUT method to update existing PlayerGWStats
        return fetch(`/api/admin/playerGWstat`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            playerId: player.playerId,
            GW: fixture?.GW,
            points: calculatedPoints,
            minutesPlayed: player.minutesPlayed,
            goalsScored: player.goalsScored,
            assists: player.assists,
            yellowCards: player.yellowCards,
            redCards: player.redCards,
            cleanSheets: player.cleanSheets,
            goalsConceded: player.goalsConceded,
            saves: player.saves,
            penaltiesSaved: player.penaltiesSaved,
            penaltiesMissed: player.penaltiesMissed,
            ownGoals: player.ownGoals,
            bonus: player.bonus,
            bps: player.bps,
            potm: player.potm,
            wasSubstituted: player.wasSubstituted,
          }),
        });
      });

      await Promise.all(savePromises);

      alert("Match data saved successfully!");
      router.push("/admin/match-events");
    } catch (error) {
      console.error("Error saving match data:", error);
      alert("Error saving match data");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!fixture) {
    return (
      <div className="text-center py-8">
        <p>Fixture not found</p>
        <Link href="/admin/match-events">
          <Button variant="outline" className="mt-4">
            Back to Match Events
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Enhanced Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <Link href="/admin/match-events">
            <Button variant="outline" size="sm" className="shrink-0">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Matches
            </Button>
          </Link>
          <div>
            <h1 className="text-4xl font-bold flex items-center gap-3 tracking-tight">
              <Activity className="h-10 w-10 text-primary" />
              Match Data Input
            </h1>
            <p className="text-lg text-muted-foreground mt-1">
              Record player performance and match statistics
            </p>
          </div>
        </div>

        {/* Quick Info */}
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="px-3 py-1">
            Round {fixture.GW}
          </Badge>
          <Badge
            variant={fixture.teamHScore !== null ? "default" : "secondary"}
            className="px-3 py-1"
          >
            {fixture.teamHScore !== null ? "Data Entered" : "Pending Input"}
          </Badge>
        </div>
      </div>

      {/* Quick Save Status */}
      {saving && (
        <Card className="border-amber-200 bg-amber-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-amber-600"></div>
              <div>
                <p className="font-medium text-amber-800">
                  Saving match data...
                </p>
                <p className="text-sm text-amber-600">
                  Please wait while we update the database
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Match Header */}
      <Card className="border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-secondary/5">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-2xl">
              <Trophy className="h-6 w-6 text-primary" />
              Gameweek {fixture.GW}
            </CardTitle>
            <Badge variant="outline" className="text-sm">
              Match ID: {fixture.id.slice(-8)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Teams and Score */}
          <div className="flex items-center justify-center gap-8">
            {/* Home Team */}
            <div className="flex flex-col items-center gap-3 min-w-[200px]">
              <Avatar className="h-16 w-16 border-2 border-primary/20">
                <AvatarImage src={fixture.teamH.logo} />
                <AvatarFallback className="text-lg font-bold">
                  {fixture.teamH.abbr}
                </AvatarFallback>
              </Avatar>
              <div className="text-center">
                <div className="font-bold text-xl">{fixture.teamH.name}</div>
                <Badge variant="secondary" className="text-xs">
                  Home
                </Badge>
              </div>
            </div>

            {/* Score Section */}
            <div className="flex flex-col items-center gap-4">
              <div className="text-center">
                <div className="text-sm text-muted-foreground mb-2">
                  Final Score
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <Label
                      htmlFor="homeScore"
                      className="text-xs text-muted-foreground"
                    >
                      Home
                    </Label>
                    <Input
                      id="homeScore"
                      type="number"
                      min="0"
                      max="20"
                      value={homeScore}
                      onChange={(e) =>
                        setHomeScore(parseInt(e.target.value) || 0)
                      }
                      className="w-16 h-16 text-center text-2xl font-bold border-2 focus:border-primary"
                    />
                  </div>
                  <div className="text-3xl font-bold text-muted-foreground px-2">
                    :
                  </div>
                  <div className="text-center">
                    <Label
                      htmlFor="awayScore"
                      className="text-xs text-muted-foreground"
                    >
                      Away
                    </Label>
                    <Input
                      id="awayScore"
                      type="number"
                      min="0"
                      max="20"
                      value={awayScore}
                      onChange={(e) =>
                        setAwayScore(parseInt(e.target.value) || 0)
                      }
                      className="w-16 h-16 text-center text-2xl font-bold border-2 focus:border-primary"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Away Team */}
            <div className="flex flex-col items-center gap-3 min-w-[200px]">
              <Avatar className="h-16 w-16 border-2 border-primary/20">
                <AvatarImage src={fixture.teamA.logo} />
                <AvatarFallback className="text-lg font-bold">
                  {fixture.teamA.abbr}
                </AvatarFallback>
              </Avatar>
              <div className="text-center">
                <div className="font-bold text-xl">{fixture.teamA.name}</div>
                <Badge variant="secondary" className="text-xs">
                  Away
                </Badge>
              </div>
            </div>
          </div>

          <Separator />

          {/* Match Details */}
          <div className="flex items-center justify-center gap-8 text-sm">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>
                {new Date(fixture.kickoffTime).toLocaleDateString("en-US", {
                  weekday: "long",
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </span>
            </div>
            <div className="flex items-center gap-2 text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>
                {new Date(fixture.kickoffTime).toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Player Data Input */}
      <Card className="border-2">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20">
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Users className="h-6 w-6 text-primary" />
            </div>
            Player Performance Data
          </CardTitle>
          <p className="text-muted-foreground">
            Input detailed statistics for each player&apos;s performance in this
            match. Fantasy points are calculated automatically based on your
            inputs.
          </p>
        </CardHeader>
        <CardContent className="p-0">
          <Tabs defaultValue="home" className="w-full">
            <div className="px-6 pt-6">
              <TabsList className="grid w-full grid-cols-2 h-14 p-1">
                <TabsTrigger
                  value="home"
                  className="flex items-center gap-3 text-base font-medium data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  <Avatar className="h-8 w-8 border-2 border-background">
                    <AvatarImage src={fixture.teamH.logo} />
                    <AvatarFallback className="text-sm font-bold">
                      {fixture.teamH.abbr}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-left">
                    <div className="font-semibold">{fixture.teamH.name}</div>
                    <div className="text-xs opacity-70">
                      {homeTeamPlayers.length} players
                    </div>
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="away"
                  className="flex items-center gap-3 text-base font-medium data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  <Avatar className="h-8 w-8 border-2 border-background">
                    <AvatarImage src={fixture.teamA.logo} />
                    <AvatarFallback className="text-sm font-bold">
                      {fixture.teamA.abbr}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-left">
                    <div className="font-semibold">{fixture.teamA.name}</div>
                    <div className="text-xs opacity-70">
                      {awayTeamPlayers.length} players
                    </div>
                  </div>
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="home" className="mt-0">
              <div className="p-6 pt-4">
                <PlayerStatsInput
                  players={homeTeamPlayers}
                  playerData={playerData}
                  updatePlayerData={updatePlayerData}
                  teamName={fixture.teamH.name}
                  teamLogo={fixture.teamH.logo}
                  teamAbbr={fixture.teamH.abbr}
                  gameweekId={gameweekId}
                />
              </div>
            </TabsContent>

            <TabsContent value="away" className="mt-0">
              <div className="p-6 pt-4">
                <PlayerStatsInput
                  players={awayTeamPlayers}
                  playerData={playerData}
                  updatePlayerData={updatePlayerData}
                  teamName={fixture.teamA.name}
                  teamLogo={fixture.teamA.logo}
                  teamAbbr={fixture.teamA.abbr}
                  gameweekId={gameweekId}
                />
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Save Section */}
      <Card className="border-primary/20 bg-primary/5">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h3 className="text-lg font-semibold">Save Match Data</h3>
              <p className="text-sm text-muted-foreground">
                Save all player statistics and match results to the database
              </p>
            </div>

            <div className="flex items-center gap-4">
              <Link href="/admin/match-events">
                <Button variant="outline" disabled={saving}>
                  Cancel
                </Button>
              </Link>

              <Button
                onClick={handleSave}
                disabled={saving}
                size="lg"
                className="min-w-[160px]"
              >
                {saving ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                {saving ? "Saving..." : "Save Match Data"}
              </Button>
            </div>
          </div>

          {saving && (
            <div className="mt-4">
              <Progress value={66} className="h-2" />
              <p className="text-xs text-muted-foreground mt-1">
                Updating player statistics and match results...
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Component for player stats input
interface PlayerStatsInputProps {
  players: PlayerInfo[];
  playerData: Record<string, PlayerMatchData>;
  updatePlayerData: (
    playerId: string,
    field: keyof PlayerMatchData,
    value: number
  ) => void;
  teamName: string;
  teamLogo?: string;
  teamAbbr?: string;
  gameweekId: string | null;
}

function PlayerStatsInput({
  players,
  playerData,
  updatePlayerData,
  teamName,
  teamLogo,
  teamAbbr,
  gameweekId,
}: PlayerStatsInputProps) {
  const StatInput = ({
    playerId,
    field,
    label,
    max = 999,
    color = "default",
  }: {
    playerId: string;
    field: keyof PlayerMatchData;
    label: string;
    max?: number;
    color?: "default" | "positive" | "negative";
  }) => {
    const value = playerData[playerId]?.[field] || 0;

    const getColorClasses = () => {
      if (color === "positive" && Number(value) > 0) {
        return "border-green-200 bg-green-50 text-green-700";
      }
      if (color === "negative" && Number(value) > 0) {
        return "border-red-200 bg-red-50 text-red-700";
      }
      return "";
    };

    return (
      <div className="flex flex-col items-center gap-1">
        <Label className="text-xs font-medium text-center min-w-[70px]">
          {label}
        </Label>
        <div className="flex items-center gap-1">
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="h-7 w-7 p-0 hover:bg-red-50"
            onClick={() => updatePlayerData(playerId, field, Number(value) - 1)}
            disabled={Number(value) <= 0}
          >
            <Minus className="h-3 w-3" />
          </Button>
          <Input
            type="number"
            min="0"
            max={max}
            value={value}
            onChange={(e) =>
              updatePlayerData(playerId, field, parseInt(e.target.value) || 0)
            }
            className={`w-14 text-center h-7 text-sm font-semibold ${getColorClasses()}`}
          />
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="h-7 w-7 p-0 hover:bg-green-50"
            onClick={() => updatePlayerData(playerId, field, Number(value) + 1)}
            disabled={Number(value) >= max}
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Team Header */}
      <div className="flex items-center gap-3 pb-4 border-b">
        <Avatar className="h-12 w-12 border-2 border-primary/20">
          <AvatarImage src={teamLogo} />
          <AvatarFallback className="text-lg font-bold">
            {teamAbbr}
          </AvatarFallback>
        </Avatar>
        <div>
          <h3 className="text-xl font-bold">{teamName}</h3>
          <p className="text-sm text-muted-foreground">
            {players.length} players
          </p>
        </div>
      </div>

      {/* Players Grid */}
      <div className="space-y-4">
        {players.map((player) => (
          <Card
            key={player.id}
            className="group hover:shadow-md transition-all duration-200"
          >
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row items-start lg:items-center gap-6">
                {/* Player Info */}
                <div className="flex items-center gap-4 min-w-[250px]">
                  <Avatar className="h-14 w-14 border-2 border-background shadow-sm">
                    <AvatarImage src={player.team.jersey} />
                    <AvatarFallback className="text-lg font-bold">
                      {player.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-bold text-lg">{player.name}</div>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge
                        className={getPlayerPositionColor(player.position)}
                      >
                        {player.position}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        #{player.id}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Stats Grid */}
                {/* Make every 4 statInput field in a new line */}
                <div className="flex-1">
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                    <StatInput
                      playerId={player.id}
                      field="minutesPlayed"
                      label="Minutes"
                      max={120}
                    />
                    <StatInput
                      playerId={player.id}
                      field="goalsScored"
                      label="Goals"
                      max={10}
                      color="positive"
                    />
                    <StatInput
                      playerId={player.id}
                      field="assists"
                      label="Assists"
                      max={10}
                      color="positive"
                    />
                    <StatInput
                      playerId={player.id}
                      field="yellowCards"
                      label="Yellow"
                      max={2}
                      color="negative"
                    />
                    <StatInput
                      playerId={player.id}
                      field="redCards"
                      label="Red"
                      max={1}
                      color="negative"
                    />
                    {player.position === "GK" && (
                      <>
                        <StatInput
                          playerId={player.id}
                          field="saves"
                          label="Saves"
                          max={20}
                          color="positive"
                        />
                        <StatInput
                          playerId={player.id}
                          field="penaltiesSaved"
                          label="Pen Saved"
                          max={5}
                          color="positive"
                        />
                        <StatInput
                          playerId={player.id}
                          field="goalsConceded"
                          label="Goals Con."
                          max={10}
                          color="negative"
                        />
                      </>
                    )}
                    {(player.position === "DEF" ||
                      player.position === "GK") && (
                      <StatInput
                        playerId={player.id}
                        field="cleanSheets"
                        label="Clean Sheet"
                        max={1}
                        color="positive"
                      />
                    )}
                    <StatInput
                      playerId={player.id}
                      field="penaltiesMissed"
                      label="Pen Missed"
                      max={5}
                      color="negative"
                    />
                    <StatInput
                      playerId={player.id}
                      field="ownGoals"
                      label="Own Goals"
                      max={5}
                      color="negative"
                    />
                  </div>
                </div>

                {/* Calculated Points */}
                <div className="text-center min-w-[120px]">
                  <div className="text-sm font-medium text-muted-foreground mb-3">
                    Fantasy Points
                  </div>
                  {(() => {
                    // Get the current gameweek stats for this player
                    const currentGWStats = player.gwStats?.find(
                      (stat: PlayerGWStats) => stat.GW === gameweekId
                    );

                    // Use form data if available, otherwise use existing GWStats, otherwise default to 0
                    const getStatValue = (
                      field: keyof PlayerMatchData
                    ): number => {
                      if (
                        playerData[player.id] &&
                        playerData[player.id][field] !== undefined
                      ) {
                        return Number(playerData[player.id][field]);
                      }
                      if (
                        currentGWStats &&
                        currentGWStats[field as keyof PlayerGWStats] !==
                          undefined
                      ) {
                        return Number(
                          currentGWStats[field as keyof PlayerGWStats]
                        );
                      }
                      return 0;
                    };

                    const stats = {
                      minutesPlayed: getStatValue("minutesPlayed"),
                      goalsScored: getStatValue("goalsScored"),
                      assists: getStatValue("assists"),
                      cleanSheets: getStatValue("cleanSheets"),
                      goalsConceded: getStatValue("goalsConceded"),
                      saves: getStatValue("saves"),
                      penaltiesSaved: getStatValue("penaltiesSaved"),
                      penaltiesMissed: getStatValue("penaltiesMissed"),
                      yellowCards: getStatValue("yellowCards"),
                      redCards: getStatValue("redCards"),
                      ownGoals: getStatValue("ownGoals"),
                    } as PlayerGWStats;

                    const totalPoints = calculatePlayerPoints(
                      stats,
                      player.position
                    );

                    return (
                      <div
                        className={`text-3xl font-bold p-4 rounded-xl border-2 shadow-sm ${
                          totalPoints > 0
                            ? "text-green-700 bg-green-50 border-green-200"
                            : totalPoints < 0
                            ? "text-red-700 bg-red-50 border-red-200"
                            : "text-gray-700 bg-gray-50 border-gray-200"
                        }`}
                      >
                        {totalPoints}
                      </div>
                    );
                  })()}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
