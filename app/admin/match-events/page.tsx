"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  PlayCircle,
  Calendar,
  Clock,
  Users,
  Target,
} from "lucide-react";
import Link from "next/link";
import { FixtureInfo } from "@/types/types";

export default function MatchEventsPage() {
  const [fixtures, setFixtures] = useState<FixtureInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRound, setSelectedRound] = useState<string>("all");

  useEffect(() => {
    fetchFixtures();
  }, []);

  const fetchFixtures = async () => {
    try {
      const response = await fetch("/api/admin/fixtures");
      const data = await response.json();
      setFixtures(data);
    } catch (error) {
      console.error("Error fetching fixtures:", error);
    } finally {
      setLoading(false);
    }
  };

  const getFixtureStatus = (fixture: FixtureInfo) => {
    if (fixture.status === "FINISHED") {
      return { status: "FINISHED", label: "Finished", color: "bg-green-500", canEdit: true };
    } else if (fixture.status === "LIVE") {
      return { status: "LIVE", label: "Live", color: "bg-red-500", canEdit: true };
    } else if (fixture.status === "UPCOMING") {
      return { status: "UPCOMING", label: "Upcoming", color: "bg-blue-500", canEdit: true };
    }
    return { status: "UPCOMING", label: "Upcoming", color: "bg-blue-500", canEdit: true };
  };

  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
    };
  };

  // Filter fixtures that can have match events input
  const availableFixtures = fixtures.filter(fixture => {
    const status = getFixtureStatus(fixture);
    const matchesRound = selectedRound === "all" || fixture.GW === selectedRound;
    return status.canEdit && matchesRound;
  });

  // Get unique rounds for filter
  const rounds = Array.from(new Set(fixtures.map(f => f.GW)));

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-4xl font-bold tracking-tight">Match Events</h1>
          <p className="text-lg text-muted-foreground mt-2">
            Input detailed match data and player statistics
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-sm px-3 py-1">
            {availableFixtures.length} matches ready
          </Badge>
        </div>
      </div>

      {/* Enhanced Stats Dashboard */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50" />
          <CardContent className="relative p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Available</p>
                <p className="text-3xl font-bold text-blue-700 dark:text-blue-300">
                  {availableFixtures.length}
                </p>
                <p className="text-xs text-blue-600/70 dark:text-blue-400/70 mt-1">
                  Ready for input
                </p>
              </div>
              <div className="p-3 bg-blue-500/10 rounded-full">
                <Calendar className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/50" />
          <CardContent className="relative p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Completed</p>
                <p className="text-3xl font-bold text-green-700 dark:text-green-300">
                  {fixtures.filter(f => getFixtureStatus(f).status === "completed").length}
                </p>
                <p className="text-xs text-green-600/70 dark:text-green-400/70 mt-1">
                  Data entered
                </p>
              </div>
              <div className="p-3 bg-green-500/10 rounded-full">
                <PlayCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/50 dark:to-red-900/50" />
          <CardContent className="relative p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-600 dark:text-red-400">Live</p>
                <p className="text-3xl font-bold text-red-700 dark:text-red-300">
                  {fixtures.filter(f => getFixtureStatus(f).status === "live").length}
                </p>
                <p className="text-xs text-red-600/70 dark:text-red-400/70 mt-1">
                  In progress
                </p>
              </div>
              <div className="p-3 bg-red-500/10 rounded-full">
                <Clock className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/50" />
          <CardContent className="relative p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Scheduled</p>
                <p className="text-3xl font-bold text-purple-700 dark:text-purple-300">
                  {fixtures.filter(f => getFixtureStatus(f).status === "scheduled").length}
                </p>
                <p className="text-xs text-purple-600/70 dark:text-purple-400/70 mt-1">
                  Upcoming
                </p>
              </div>
              <div className="p-3 bg-purple-500/10 rounded-full">
                <Target className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filter and Matches Section */}
      <div className="space-y-6">
        {/* Filter Bar */}
        <Card className="border-dashed">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-muted-foreground" />
                  <span className="font-medium">Filter Matches</span>
                </div>
                <Select value={selectedRound} onValueChange={setSelectedRound}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="All Rounds" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Rounds</SelectItem>
                    {rounds.map((round) => (
                      <SelectItem key={round} value={round.toString()}>
                        Round {round}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="px-3 py-1">
                  {availableFixtures.length} matches available
                </Badge>
                <Badge variant="outline" className="px-3 py-1">
                  {rounds.length} rounds total
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Available Matches */}
        {availableFixtures.length === 0 ? (
          <Card className="border-dashed">
            <CardContent className="p-12">
              <div className="text-center">
                <div className="p-4 bg-muted/50 rounded-full w-fit mx-auto mb-4">
                  <PlayCircle className="h-12 w-12 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2">No matches available</h3>
                <p className="text-muted-foreground mb-4">
                  Matches must be live or completed to input data
                </p>
                <Badge variant="outline">
                  Check back after matches have started
                </Badge>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {availableFixtures.map((fixture) => {
              const status = getFixtureStatus(fixture);
              const dateTime = formatDateTime(
                typeof fixture.kickoffTime === "string"
                  ? fixture.kickoffTime
                  : fixture.kickoffTime.toISOString()
              );

              return (
                <Card key={fixture.id} className="group hover:shadow-lg transition-all duration-200 border-l-4 border-l-transparent hover:border-l-primary">
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
                      {/* Match Info */}
                      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 flex-1">
                        {/* Round Badge */}
                        <Badge variant="secondary" className="shrink-0">
                          Round {fixture.GW}
                        </Badge>

                        {/* Teams */}
                        <div className="flex items-center gap-4 min-w-0 flex-1">
                          {/* Home Team */}
                          <div className="flex items-center gap-3 min-w-0">
                            <Avatar className="h-12 w-12 border-2 border-background shadow-sm">
                              <AvatarImage src={fixture.teamH.logo} />
                              <AvatarFallback className="text-sm font-bold">
                                {fixture.teamH.abbr}
                              </AvatarFallback>
                            </Avatar>
                            <div className="min-w-0">
                              <p className="font-semibold truncate">{fixture.teamH.name}</p>
                              <p className="text-xs text-muted-foreground">Home</p>
                            </div>
                          </div>

                          {/* Score */}
                          <div className="flex items-center justify-center px-4">
                            {fixture.teamHScore !== null && fixture.teamAScore !== null ? (
                              <div className="text-center">
                                <div className="text-2xl font-bold">
                                  {fixture.teamHScore} - {fixture.teamAScore}
                                </div>
                                <div className="text-xs text-muted-foreground">Final</div>
                              </div>
                            ) : (
                              <div className="text-center">
                                <div className="text-lg font-medium text-muted-foreground">VS</div>
                                <div className="text-xs text-muted-foreground">
                                  {status.status === "live" ? "Live" : "Scheduled"}
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Away Team */}
                          <div className="flex items-center gap-3 min-w-0">
                            <div className="min-w-0 text-right">
                              <p className="font-semibold truncate">{fixture.teamA.name}</p>
                              <p className="text-xs text-muted-foreground">Away</p>
                            </div>
                            <Avatar className="h-12 w-12 border-2 border-background shadow-sm">
                              <AvatarImage src={fixture.teamA.logo} />
                              <AvatarFallback className="text-sm font-bold">
                                {fixture.teamA.abbr}
                              </AvatarFallback>
                            </Avatar>
                          </div>
                        </div>
                      </div>

                      {/* Match Details & Action */}
                      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                        {/* Date & Time */}
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Calendar className="h-4 w-4" />
                          <div className="text-center">
                            <div className="font-medium">{dateTime.date}</div>
                            <div className="flex items-center gap-1 justify-center">
                              <Clock className="h-3 w-3" />
                              {dateTime.time}
                            </div>
                          </div>
                        </div>

                        {/* Status & Action */}
                        <div className="flex items-center gap-3">
                          <Badge
                            variant={status.status === "completed" ? "default" : status.status === "live" ? "destructive" : "secondary"}
                            className="shrink-0"
                          >
                            {status.label}
                          </Badge>

                          <Link href={`/admin/match-events/${fixture.id}`}>
                            <Button className="group-hover:shadow-md transition-shadow">
                              <Users className="h-4 w-4 mr-2" />
                              Input Data
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
