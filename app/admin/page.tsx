"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Users,
  Calendar,
  Trophy,
  Activity,
  TrendingUp,
  Clock,
  CheckCircle
} from "lucide-react";
import Link from "next/link";
import { FixtureInfo } from "@/types/types";

interface AdminStats {
  totalTeams: number;
  totalPlayers: number;
  totalFixtures: number;
  completedFixtures: number;
  upcomingFixtures: number;
  liveFixtures: number;
  recentFixtures: FixtureInfo[];
}



export default function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/admin/stats');
        if (response.ok) {
          const data = await response.json();
          setStats(data);
        }
      } catch (error) {
        console.error('Error fetching admin stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-8">
        <p>Failed to load admin statistics</p>
      </div>
    );
  }

  const statCards = [
    {
      title: "Total Teams",
      value: stats.totalTeams,
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Total Players",
      value: stats.totalPlayers,
      icon: Trophy,
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "Total Fixtures",
      value: stats.totalFixtures,
      icon: Calendar,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      title: "Live Matches",
      value: stats.liveFixtures,
      icon: Activity,
      color: "text-red-600",
      bgColor: "bg-red-50",
    },
  ];

  const quickActions = [
    {
      title: "Add Team",
      description: "Create a new team",
      href: "/admin/teams",
      icon: Users,
      color: "bg-blue-500",
    },
    {
      title: "Add Fixture",
      description: "Schedule a new match",
      href: "/admin/fixtures",
      icon: Calendar,
      color: "bg-green-500",
    },
    {
      title: "Match Input",
      description: "Input live match events",
      href: "/admin/match-events",
      icon: Activity,
      color: "bg-red-500",
    },
    {
      title: "Manage Players",
      description: "Add or edit players",
      href: "/admin/players",
      icon: Trophy,
      color: "bg-purple-500",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard Overview</h1>
        <p className="text-gray-600 mt-2">
          Welcome to the admin dashboard. Here&#39;s what&#39;s happening in your league.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat) => (
          <Card key={stat.title}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <Link key={action.title} href={action.href}>
                <Card className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${action.color}`}>
                        <action.icon className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">{action.title}</h3>
                        <p className="text-sm text-gray-600">{action.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Fixture Status Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Fixture Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">Completed</span>
                </div>
                <Badge variant="outline">{stats.completedFixtures}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Activity className="h-4 w-4 text-red-600" />
                  <span className="text-sm font-medium">Live</span>
                </div>
                <Badge variant="outline">{stats.liveFixtures}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">Upcoming</span>
                </div>
                <Badge variant="outline">{stats.upcomingFixtures}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Recent Fixtures
              </span>
              <Link href="/admin/fixtures">
                <Button variant="outline" size="sm">
                  View All
                </Button>
              </Link>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.recentFixtures.length > 0 ? (
                stats.recentFixtures.map((fixture: FixtureInfo) => (
                  <div key={fixture.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline" className="text-xs">
                        R{fixture.GW}
                      </Badge>
                      <span className="text-sm font-medium">
                        {fixture.teamH.abbr} vs {fixture.teamA.abbr}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      {fixture.teamHScore !== null && fixture.teamAScore !== null
                        ? `${fixture.teamHScore}-${fixture.teamAScore}`
                        : new Date(fixture.kickoffTime).toLocaleDateString()
                      }
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-600 text-center py-4">
                  No fixtures found
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
