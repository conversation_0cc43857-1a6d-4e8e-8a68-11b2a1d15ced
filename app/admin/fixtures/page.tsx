"use client";

import { useState, useEffect, use<PERSON>emo } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Filter,
  Calendar,
  Clock,
  PlayCircle,
} from "lucide-react";
import Link from "next/link";
import { <PERSON>Week, Team } from "@prisma/client";
import { toast } from "sonner";
import { FixtureInfo } from "@/types/types";

export default function FixturesManagement() {
  const [fixtures, setFixtures] = useState<FixtureInfo[]>([]);
  const [currentSeasonGameweeks, setCurrentSeasonGameweeks] = useState<
    GameWeek[]
  >([]);
  const [selectedGameweek, setSelectedGameweek] = useState<GameWeek | null>(
    null
  );
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTeam, setSelectedTeam] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedFixtures, setSelectedFixtures] = useState<string[]>([]);
  const [editingFixture, setEditingFixture] = useState<FixtureInfo | null>(
    null
  );
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    GW: "",
    kickoffTime: "",
    teamHId: "",
    teamAId: "",
    teamHScore: "",
    teamAScore: "",
  });

  useEffect(() => {
    const initialize = async () => {
      try {
        const teamResponse = await fetch("/api/admin/teams");
        if (!teamResponse.ok) {
          toast.error("Failed to fetch teams");
          return;
        }
        const fixtureResponse = await fetch("/api/admin/fixtures");
        if (!fixtureResponse.ok) {
          toast.error("Failed to fetch fixtures");
          return;
        }
        const teamData = await teamResponse.json();
        const fixtureData = await fixtureResponse.json();
        setFixtures(fixtureData);
        setTeams(teamData);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching teams:", error);
        toast.error("Error fetching teams");
      }
    };
    initialize();
  }, []);

  useEffect(() => {
    const fetchFixtures = async () => {
      try {
        const params = new URLSearchParams();
        if (selectedGameweek)
          params.append("GW", selectedGameweek.GW.toString());
        if (selectedTeam !== "all") params.append("teamId", selectedTeam);
        if (selectedStatus !== "all") params.append("status", selectedStatus);

        const response = await fetch(
          `/api/admin/fixtures?${params.toString()}`
        );
        if (response.ok) {
          const data = await response.json();
          setFixtures(data);
        } else {
          toast.error("Failed to fetch fixtures");
        }
      } catch (error) {
        console.error("Error fetching fixtures:", error);
        toast.error("Error fetching fixtures");
      } finally {
        setLoading(false);
      }
    };
    if (!loading) {
      fetchFixtures();
    }
  }, [selectedGameweek, selectedTeam, selectedStatus, loading]);

  const fetchCurrentSeasonGameweeks = async (season: string) => {
    try {
      const response = await fetch(`/api/admin/gameweeks?season=${season}`);
      if (response.ok) {
        const data = await response.json();
        setCurrentSeasonGameweeks(data);
        setSelectedGameweek(data[0]);
      }
    } catch (error) {
      console.error("Error fetching current season gameweeks:", error);
    }
  };

  useEffect(() => {
    fetchCurrentSeasonGameweeks("2025");
  }, []);

  const handleGameweekChange = (value: string) => {
    const gameweek = currentSeasonGameweeks.find((gw) => gw.id === value);
    setSelectedGameweek(gameweek || null);
  };

  const getFixtureStatus = (fixture: FixtureInfo) => {
    if (fixture.status === "FINISHED") {
      return { status: "FINISHED", label: "Finished", color: "bg-green-500" };
    } else if (fixture.status === "LIVE") {
      return { status: "LIVE", label: "Live", color: "bg-red-500" };
    } else if (fixture.status === "UPCOMING") {
      return { status: "UPCOMING", label: "Scheduled", color: "bg-blue-500" };
    }
  };

  const handleCreateFixture = async () => {
    if (
      !formData.GW ||
      !formData.kickoffTime ||
      !formData.teamHId ||
      !formData.teamAId
    ) {
      toast.error("All fields are required");
      return;
    }

    try {
      const response = await fetch("/api/admin/fixtures", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const newFixture = await response.json();
        setFixtures([...fixtures, newFixture]);
        setCreateDialogOpen(false);
        setFormData({
          GW: "",
          kickoffTime: "",
          teamHId: "",
          teamAId: "",
          teamHScore: "",
          teamAScore: "",
        });
        toast.success("Fixture created successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to create fixture");
      }
    } catch (error) {
      console.error("Error creating fixture:", error);
      toast.error("Error creating fixture");
    }
  };

  const handleDeleteFixture = async (fixtureId: string) => {
    if (!confirm("Are you sure you want to delete this fixture?")) return;

    try {
      const response = await fetch(`/api/admin/fixtures/${fixtureId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setFixtures(fixtures.filter((f) => f.id !== fixtureId));
        toast.success("Fixture deleted successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to delete fixture");
      }
    } catch (error) {
      console.error("Error deleting fixture:", error);
      toast.error("Error deleting fixture");
    }
  };

  const handleEditFixture = (fixture: FixtureInfo) => {
    setEditingFixture(fixture);
    setFormData({
      GW: fixture.GW.toString(),
      kickoffTime: new Date(fixture.kickoffTime).toISOString().slice(0, 16),
      teamHId: fixture.teamHId,
      teamAId: fixture.teamAId,
      teamHScore: fixture.teamHScore?.toString() || "",
      teamAScore: fixture.teamAScore?.toString() || "",
    });
  };

  const handleUpdateFixture = async () => {
    if (!editingFixture) return;

    try {
      const response = await fetch(`/api/admin/fixtures/${editingFixture.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const updatedFixture = await response.json();
        setFixtures(
          fixtures.map((f) => (f.id === editingFixture.id ? updatedFixture : f))
        );
        setEditingFixture(null);
        setFormData({
          GW: "",
          kickoffTime: "",
          teamHId: "",
          teamAId: "",
          teamHScore: "",
          teamAScore: "",
        });
        toast.success("Fixture updated successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to update fixture");
      }
    } catch (error) {
      console.error("Error updating fixture:", error);
      toast.error("Error updating fixture");
    }
  };

  const toggleFixtureSelection = (fixtureId: string) => {
    setSelectedFixtures((prev) =>
      prev.includes(fixtureId)
        ? prev.filter((id) => id !== fixtureId)
        : [...prev, fixtureId]
    );
  };

  const toggleSelectAll = (checked: boolean | "indeterminate") => {
    if (checked === true) {
      setSelectedFixtures(filteredFixtures.map((f) => f.id));
    } else {
      setSelectedFixtures([]);
    }
  };

  const filteredFixtures = useMemo(
    () =>
      fixtures.filter((fixture) => {
        const matchesSearch =
          fixture.teamH.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          fixture.teamA.name.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesRound =
          !selectedGameweek || fixture.GW === selectedGameweek.id;
        const matchesTeam =
          selectedTeam === "all" ||
          fixture.teamHId === selectedTeam ||
          fixture.teamAId === selectedTeam;

        let matchesStatus = true;
        if (selectedStatus !== "all") {
          const status = getFixtureStatus(fixture)?.status;
          matchesStatus = status === selectedStatus;
        }
        return matchesSearch && matchesRound && matchesTeam && matchesStatus;
      }),
    [fixtures, searchTerm, selectedGameweek, selectedTeam, selectedStatus]
  );

  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
    };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Fixture Management</h1>
          <p className="text-muted-foreground">
            Manage matches, input results, and generate fixtures
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Fixture
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Fixture</DialogTitle>
                <DialogDescription>
                  Add a new fixture to the system
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="GW">Round</Label>
                    <Select
                      value={formData.GW}
                      onValueChange={(value) =>
                        setFormData({ ...formData, GW: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select GW" />
                      </SelectTrigger>
                      <SelectContent>
                        {currentSeasonGameweeks.map((gw) => (
                          <SelectItem key={gw.id} value={gw.id}>
                            Gameweek {gw.GW}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="kickoffTime">Kickoff Time</Label>
                    <Input
                      id="kickoffTime"
                      type="datetime-local"
                      value={formData.kickoffTime}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          kickoffTime: e.target.value,
                        })
                      }
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="teamH">Home Team</Label>
                    <Select
                      value={formData.teamHId}
                      onValueChange={(value) =>
                        setFormData({ ...formData, teamHId: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select home team" />
                      </SelectTrigger>
                      <SelectContent>
                        {teams.map((team) => (
                          <SelectItem key={team.id} value={team.id}>
                            {team.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="teamA">Away Team</Label>
                    <Select
                      value={formData.teamAId}
                      onValueChange={(value) =>
                        setFormData({ ...formData, teamAId: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select away team" />
                      </SelectTrigger>
                      <SelectContent>
                        {teams.map((team) => (
                          <SelectItem key={team.id} value={team.id}>
                            {team.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button onClick={handleCreateFixture}>Create Fixture</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search fixtures..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select
              value={selectedGameweek?.id || ""}
              onValueChange={handleGameweekChange}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Gameweeks" />
              </SelectTrigger>
              <SelectContent>
                {currentSeasonGameweeks.map((gw) => (
                  <SelectItem key={gw.id} value={gw.id}>
                    Gameweek {gw.GW} - {gw.season}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedTeam} onValueChange={setSelectedTeam}>
              <SelectTrigger>
                <SelectValue placeholder="All Teams" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Teams</SelectItem>
                {teams.map((team) => (
                  <SelectItem key={team.id} value={team.id}>
                    {team.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="UPCOMING">Upcoming</SelectItem>
                <SelectItem value="LIVE">Live</SelectItem>
                <SelectItem value="FINISHED">Finished</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {filteredFixtures.length} fixture
                {filteredFixtures.length !== 1 ? "s" : ""}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Fixtures Table */}
      <Card>
        <CardHeader>
          <CardTitle>Fixtures ({filteredFixtures.length})</CardTitle>
        </CardHeader>
        <CardContent className="p-0 sm:p-6">
          {/* Mobile Cards View */}
          <div className="block sm:hidden">
            <div className="space-y-3 p-3">
              {filteredFixtures.map((fixture) => {
                const status = getFixtureStatus(fixture);
                const dateTime = formatDateTime(
                  typeof fixture.kickoffTime === "string"
                    ? fixture.kickoffTime
                    : fixture.kickoffTime.toISOString()
                );

                return (
                  <Card key={fixture.id} className="p-4">
                    <div className="space-y-3">
                      {/* Selection and Round and Status */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Checkbox
                            checked={selectedFixtures.includes(fixture.id)}
                            onCheckedChange={() =>
                              toggleFixtureSelection(fixture.id)
                            }
                          />
                          <Badge variant="outline">Round {fixture.GW}</Badge>
                        </div>
                        <Badge className={`${status?.color} text-white text-xs`}>
                          {status?.label}
                        </Badge>
                      </div>

                      {/* Teams */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={fixture.teamH.logo} />
                            <AvatarFallback className="text-xs">
                              {fixture.teamH.abbr}
                            </AvatarFallback>
                          </Avatar>
                          <span className="font-medium text-sm truncate">
                            {fixture.teamH.abbr}
                          </span>
                        </div>

                        <div className="text-center px-3">
                          {fixture.teamHScore !== null &&
                          fixture.teamAScore !== null ? (
                            <div className="font-bold text-lg">
                              {fixture.teamHScore} - {fixture.teamAScore}
                            </div>
                          ) : (
                            <span className="text-muted-foreground font-medium">
                              VS
                            </span>
                          )}
                        </div>

                        <div className="flex items-center gap-2 flex-1 min-w-0 justify-end">
                          <span className="font-medium text-sm truncate">
                            {fixture.teamA.abbr}
                          </span>
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={fixture.teamA.logo} />
                            <AvatarFallback className="text-xs">
                              {fixture.teamA.abbr}
                            </AvatarFallback>
                          </Avatar>
                        </div>
                      </div>

                      {/* Date and Actions */}
                      <div className="flex items-center justify-between">
                        <div className="text-xs text-muted-foreground">
                          {dateTime.date} • {dateTime.time}
                        </div>
                        <div className="flex items-center gap-1">
                          {status?.status !== "completed" && (
                            <Link href={`/admin/match-events/${fixture.id}`}>
                              <Button variant="outline" size="sm">
                                <PlayCircle className="h-4 w-4" />
                              </Button>
                            </Link>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditFixture(fixture)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteFixture(fixture.id)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Desktop Table View */}
          <div className="hidden sm:block overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={
                        selectedFixtures.length === filteredFixtures.length &&
                        filteredFixtures.length > 0
                      }
                      onCheckedChange={toggleSelectAll}
                    />
                  </TableHead>
                  <TableHead>Round</TableHead>
                  <TableHead>Match</TableHead>
                  <TableHead>Date & Time</TableHead>
                  <TableHead>Score</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredFixtures.map((fixture) => {
                  const status = getFixtureStatus(fixture);
                  const dateTime = formatDateTime(
                    typeof fixture.kickoffTime === "string"
                      ? fixture.kickoffTime
                      : fixture.kickoffTime.toISOString()
                  );

                  return (
                    <TableRow key={fixture.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedFixtures.includes(fixture.id)}
                          onCheckedChange={() =>
                            toggleFixtureSelection(fixture.id)
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">Round {fixture.GW}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-4">
                          {/* Home Team */}
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={fixture.teamH.logo} />
                              <AvatarFallback>
                                {fixture.teamH.abbr}
                              </AvatarFallback>
                            </Avatar>
                            <span className="font-medium">
                              {fixture.teamH.name}
                            </span>
                          </div>

                          <span className="text-muted-foreground">vs</span>

                          {/* Away Team */}
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={fixture.teamA.logo} />
                              <AvatarFallback>
                                {fixture.teamA.abbr}
                              </AvatarFallback>
                            </Avatar>
                            <span className="font-medium">
                              {fixture.teamA.name}
                            </span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="font-medium">{dateTime.date}</div>
                            <div className="text-sm text-muted-foreground flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {dateTime.time}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {fixture.teamHScore !== null &&
                        fixture.teamAScore !== null ? (
                          <div className="font-bold text-lg">
                            {fixture.teamHScore} - {fixture.teamAScore}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge className={`${status?.color} text-white`}>
                          {status?.label}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          {status?.status !== "completed" && (
                            <Link href={`/admin/match-events/${fixture.id}`}>
                              <Button variant="outline" size="sm">
                                <PlayCircle className="h-4 w-4" />
                              </Button>
                            </Link>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditFixture(fixture)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteFixture(fixture.id)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Edit Fixture Dialog */}
      {editingFixture && (
        <Dialog
          open={!!editingFixture}
          onOpenChange={(open) => !open && setEditingFixture(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Fixture</DialogTitle>
              <DialogDescription>
                Update fixture information and scores
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-GW">Round</Label>
                  <Input
                    id="edit-GW"
                    type="number"
                    value={formData.GW}
                    onChange={(e) =>
                      setFormData({ ...formData, GW: e.target.value })
                    }
                    placeholder="Enter GW number"
                  />
                </div>
                <div>
                  <Label htmlFor="edit-kickoffTime">Kickoff Time</Label>
                  <Input
                    id="edit-kickoffTime"
                    type="datetime-local"
                    value={formData.kickoffTime}
                    onChange={(e) =>
                      setFormData({ ...formData, kickoffTime: e.target.value })
                    }
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-teamH">Home Team</Label>
                  <Select
                    value={formData.teamHId}
                    onValueChange={(value) =>
                      setFormData({ ...formData, teamHId: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select home team" />
                    </SelectTrigger>
                    <SelectContent>
                      {teams.map((team) => (
                        <SelectItem key={team.id} value={team.id}>
                          {team.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-teamA">Away Team</Label>
                  <Select
                    value={formData.teamAId}
                    onValueChange={(value) =>
                      setFormData({ ...formData, teamAId: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select away team" />
                    </SelectTrigger>
                    <SelectContent>
                      {teams.map((team) => (
                        <SelectItem key={team.id} value={team.id}>
                          {team.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-teamHScore">Home Score</Label>
                  <Input
                    id="edit-teamHScore"
                    type="number"
                    value={formData.teamHScore}
                    onChange={(e) =>
                      setFormData({ ...formData, teamHScore: e.target.value })
                    }
                    placeholder="Home team score"
                    min="0"
                  />
                </div>
                <div>
                  <Label htmlFor="edit-teamAScore">Away Score</Label>
                  <Input
                    id="edit-teamAScore"
                    type="number"
                    value={formData.teamAScore}
                    onChange={(e) =>
                      setFormData({ ...formData, teamAScore: e.target.value })
                    }
                    placeholder="Away team score"
                    min="0"
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditingFixture(null)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateFixture}>Update Fixture</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
