generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model  GameWeek {
  id         String   @id @default(cuid()) @db.Var<PERSON>har(30)
  season     String   @db.VarChar(10)    // e.g., "2025"
  GW         Int      @db.SmallInt       // Small int for gameweek number
  isActive   Boolean  @default(false)    // Replace ActiveGameWeek table
  status     Status?  @default(UPCOMING)
  deadline   DateTime?                   // Transfer deadline
  userGWSheets UserGWSheet[]
  playerGWStats PlayerGWStats[]
  fixtures   Fixture[]
  chipsUsed  UserChip[]

  @@unique([season, GW])
  @@index([GW, season])
  @@index([isActive])
}



model User {
  id              String        @id @default(cuid()) @db.VarChar(40)
  email           String        @unique @db.VarChar(255)
  name            String?       @db.VarChar(100)
  phone           String?       @db.VarChar(15)
  country         String?       @db.VarChar(15)        // ISO country code
  isAd<PERSON>       @default(false)
  isPhoneVerified Boolean       @default(false)
  createdAt       DateTime      @default(now()) @db.Timestamptz
  updatedAt       DateTime      @updatedAt @db.Timestamptz
  totalPoints     Int           @default(0) @db.Integer
  transfersLeft   Int           @default(0) @db.SmallInt
  moneyLeft       Decimal       @default(100.0) @db.Decimal(5,1) // 999.9 max
  favoriteClub    String?       @db.VarChar(30)
  hasOnboarded    Boolean       @default(false)
  gwSheets        UserGWSheet[]
  chips           UserChip[]
  leaguesAsAdmin  League[]      @relation("LeagueAdmins")
  leaguesAsMember League[]      @relation("UserLeagues")

  @@index([totalPoints])
  @@index([createdAt])
}

model UserGWSheet {
  id            String   @id @default(cuid()) @db.VarChar(30)
  userId        String   @db.VarChar(40)
  GW            String   @db.VarChar(30)
  captainId     String?  @db.VarChar(30)
  viceCaptainId String?  @db.VarChar(30)
  createdAt     DateTime @default(now())
  starters      String[] @db.VarChar(30)  // Array of player IDs
  totalPoints   Int      @default(0) @db.Integer
  captainPoints Int      @default(0) @db.SmallInt
  viceCaptainPoints Int  @default(0) @db.SmallInt
  updatedAt     DateTime @updatedAt
  subs          String[] @db.VarChar(30)  // Array of player IDs
  transfersMade Int      @default(0) @db.SmallInt // Track transfers for this GW
  pointsOnBench Int      @default(0) @db.SmallInt // Points left on bench
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  gameWeek      GameWeek @relation(fields: [GW], references: [id], onDelete: Cascade)

  @@unique([userId, GW])
  @@index([totalPoints])
  @@index([GW])
}

model League {
  id      String @id @default(cuid()) @db.VarChar(30)
  name    String @db.VarChar(100)
  admins  User[] @relation("LeagueAdmins")
  members User[] @relation("UserLeagues")
}

model Player {
  id           String            @id @default(cuid()) @db.VarChar(30)
  name         String            @db.VarChar(100)
  teamId       String            @db.VarChar(30)
  currentPrice Decimal           @db.Decimal(3, 1)
  position     Position
  isSuspended  Boolean           @default(false) // For injured/suspended players
  SuspensionType String?         @db.VarChar(10) // DTP, Suspend, etc.
  suspendedUntil DateTime?       @db.Timestamptz // Date until suspended
  team         Team              @relation(fields: [teamId], references: [id])
  gwStats      PlayerGWStats[]   @relation("PlayerToGWStats")
  totalStats   PlayerTotalStats?

  @@index([position])
  @@index([teamId])
}

model PlayerGWStats {
  id              String  @id @default(cuid()) @db.VarChar(30)
  GW              String  @db.VarChar(30)
  playerId        String  @db.VarChar(30)
  price           Int     @db.SmallInt        // Price in 0.1M units
  points          Int     @db.SmallInt        // Total points for GW
  transfersIn     Int     @default(0) @db.Integer
  transfersOut    Int     @default(0) @db.Integer
  owners          Int     @default(0) @db.Integer
  minutesPlayed   Int     @default(0) @db.SmallInt
  goalsScored     Int     @default(0) @db.SmallInt
  assists         Int     @default(0) @db.SmallInt
  cleanSheets     Int     @default(0) @db.SmallInt
  goalsConceded   Int     @default(0) @db.SmallInt
  ownGoals        Int     @default(0) @db.SmallInt
  penaltiesSaved  Int     @default(0) @db.SmallInt
  penaltiesMissed Int     @default(0) @db.SmallInt
  yellowCards     Int     @default(0) @db.SmallInt
  redCards        Int     @default(0) @db.SmallInt
  saves           Int     @default(0) @db.SmallInt
  potm            Boolean @default(false)
  bonus           Int     @default(0) @db.SmallInt
  bps             Int     @default(0) @db.SmallInt
  wasSubstituted  Boolean @default(false)    // Track if player was subbed
  player          Player  @relation("PlayerToGWStats", fields: [playerId], references: [id])
  gameWeek        GameWeek @relation(fields: [GW], references: [id])

  @@unique([playerId, GW])
  @@index([GW])
  @@index([points])
  @@index([playerId])
}

model PlayerTotalStats {
  id              String @id @default(cuid()) @db.VarChar(30)
  playerId        String @unique @db.VarChar(30)
  points          Int    @default(0) @db.Integer
  minutesPlayed   Int    @default(0) @db.Integer
  goalsScored     Int    @default(0) @db.SmallInt
  assists         Int    @default(0) @db.SmallInt
  cleanSheets     Int    @default(0) @db.SmallInt
  goalsConceded   Int    @default(0) @db.SmallInt
  ownGoals        Int    @default(0) @db.SmallInt
  penaltiesSaved  Int    @default(0) @db.SmallInt
  penaltiesMissed Int    @default(0) @db.SmallInt
  yellowCards     Int    @default(0) @db.SmallInt
  redCards        Int    @default(0) @db.SmallInt
  saves           Int    @default(0) @db.SmallInt
  potm            Int    @default(0) @db.SmallInt
  bonus           Int    @default(0) @db.SmallInt
  bps             Int    @default(0) @db.SmallInt
  player          Player @relation(fields: [playerId], references: [id])

  @@index([points])
}

model Team {
  id           String    @id @default(cuid()) @db.VarChar(30)
  name         String    @db.VarChar(100)
  abbr         String    @db.VarChar(5)
  logo         String    @db.VarChar(255)
  jersey       String    @db.VarChar(255)
  awayFixtures Fixture[] @relation("AwayFixtures")
  homeFixtures Fixture[] @relation("HomeFixtures")
  players      Player[]

  @@index([abbr])
}

model Fixture {
  id          String        @id @default(cuid()) @db.VarChar(30)
  GW          String        @db.VarChar(30)
  kickoffTime DateTime
  teamHId     String        @db.VarChar(30)
  teamAId     String        @db.VarChar(30)
  teamHScore  Int?          @db.SmallInt
  teamAScore  Int?          @db.SmallInt
  status      Status?       @default(UPCOMING)
  gameWeek    GameWeek      @relation(fields: [GW], references: [id])
  teamA       Team          @relation("AwayFixtures", fields: [teamAId], references: [id])
  teamH       Team          @relation("HomeFixtures", fields: [teamHId], references: [id])
  stats       FixtureStat[]

  @@index([kickoffTime])
}

model FixtureStat {
  id         String         @id @default(cuid()) @db.VarChar(30)
  fixtureId  String         @db.VarChar(30)
  identifier StatIdentifier
  h          Json
  a          Json
  fixture    Fixture        @relation(fields: [fixtureId], references: [id])

  @@index([fixtureId])
}

enum Position {
  GK
  DEF
  MID
  ATK
}

enum Role {
  C
  V
}

enum StatIdentifier {
  GOALS_SCORED
  ASSISTS
  YELLOW_CARDS
  RED_CARDS
  SAVES
  PENALTIES_SAVED
  PENALTIES_MISSED
  OWN_GOALS
  BONUS
  POTM
  BPS
  CLEAN_SHEETS
}

enum SuspensionType {
  DTP
  SUSPENDED
  INJURED
}

enum Status {
  UPCOMING
  LIVE
  FINISHED
}

enum CHIP {
  WILDCARD
  DOUBLE_CAPTAIN
  MAX_CAPTAIN
  TRIPLE_CAPTAIN
  BENCH_BOOST
}

model UserChip {
  id        String   @id @default(cuid()) @db.VarChar(30)
  userId    String   @db.VarChar(40)
  chip      CHIP
  usedInGW  String?  @db.VarChar(30)  // GameWeek ID when used
  isUsed    Boolean  @default(false)
  period    String   @db.VarChar(10)     // 1 for GW 1-15, 2 for GW 16-30
  season    String   @db.VarChar(10)
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  gameWeek  GameWeek? @relation(fields: [usedInGW], references: [id])

  @@unique([userId, chip, period, season])
  @@index([userId])
  @@index([chip])
}