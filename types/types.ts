import { Prisma } from "@prisma/client";

export type Role = "C" | "V" | null;

export type Position = "GK" | "DEF" | "MID" | "ATK";

export type ViewMode = "list" | "pitch";

export type Status = "UPCOMING" | "LIVE" | "FINISHED";

export type PlayerInfo = Prisma.PlayerGetPayload<{
  include: { team: true; totalStats: true; gwStats: true };
}>;

export type TeamInfo = Prisma.TeamGetPayload<{
  include: { players: true; homeFixtures: true; awayFixtures: true };
}>;

export type FixtureInfo = Prisma.FixtureGetPayload<{
  include: { teamH: true; teamA: true; stats: true };
}>;

export type GameWeekInfo = Prisma.GameWeekGetPayload<{
  include: { fixtures: true };
}>;
