"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Calendar, Save, Loader2 } from "lucide-react";
import { GameWeek, Team } from "@prisma/client";
import { FixtureInfo } from "@/types/types";

interface EditFixtureDialogProps {
  fixture: FixtureInfo | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (fixture: FixtureInfo) => void;
}

export default function EditFixtureDialog({
  fixture,
  open,
  onOpenChange,
  onSave,
}: EditFixtureDialogProps) {
  const [currentSeasonGameweeks, setCurrentSeasonGameweeks] = useState<GameWeek[]>([]);
  const [selectedGameweek, setSelectedGameweek] = useState<GameWeek | null>(null);
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    GW: "",
    kickoffTime: "",
    teamHId: "",
    teamAId: "",
    teamHScore: "",
    teamAScore: "",
  });
  
  const fetchCurrentSeasonGameweeks = async (season: string) => {
    try {
      const response = await fetch(`/api/admin/gameweeks?season=${season}`);
      if (response.ok) {
        const data = await response.json();
        setCurrentSeasonGameweeks(data);
        setSelectedGameweek(data[0]);
      }
    } catch (error) {
      console.error("Error fetching current season gameweeks:", error);
    }
  };
  
  const handleGameweekChange = (gameweekId: string) => {
    const selectedGameweek = currentSeasonGameweeks.find((gw) => gw.id === gameweekId);
    if (selectedGameweek) {
      setSelectedGameweek(selectedGameweek);
      setFormData({ ...formData, GW: selectedGameweek.id });
    }
  };

  useEffect(() => {
    fetchCurrentSeasonGameweeks("2025");
  }, []);

  useEffect(() => {
    if (open) {
      fetchTeams();
      if (fixture) {
        setFormData({
          GW: fixture.GW,
          kickoffTime: new Date(fixture.kickoffTime).toISOString().slice(0, 16),
          teamHId: fixture.teamHId,
          teamAId: fixture.teamAId,
          teamHScore: fixture.teamHScore?.toString() || "",
          teamAScore: fixture.teamAScore?.toString() || "",
        });
      }
    }
  }, [open, fixture]);

  const fetchTeams = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/teams");
      const data = await response.json();
      setTeams(data);
    } catch (error) {
      console.error("Error fetching teams:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!fixture) return;

    setSaving(true);
    try {
      const response = await fetch(`/api/admin/fixtures/${fixture.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          GW: parseInt(formData.GW),
          kickoffTime: new Date(formData.kickoffTime).toISOString(),
          teamHId: formData.teamHId,
          teamAId: formData.teamAId,
          teamHScore: formData.teamHScore ? parseInt(formData.teamHScore) : null,
          teamAScore: formData.teamAScore ? parseInt(formData.teamAScore) : null,
        }),
      });

      if (response.ok) {
        const updatedFixture = await response.json();
        onSave(updatedFixture);
        onOpenChange(false);
      } else {
        const error = await response.json();
        alert(error.error || "Failed to update fixture");
      }
    } catch (error) {
      console.error("Error updating fixture:", error);
      alert("Error updating fixture");
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const selectedHomeTeam = teams.find(t => t.id === formData.teamHId);
  const selectedAwayTeam = teams.find(t => t.id === formData.teamAId);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Edit Fixture
          </DialogTitle>
          <DialogDescription>
            Update fixture details, teams, and scores.
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        ) : (
          <div className="space-y-6">
            {/* Round and Date/Time */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                              <Select
                value={selectedGameweek?.id || ""}
                onValueChange={handleGameweekChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select current gameweek" />
                </SelectTrigger>
                <SelectContent>
                  {currentSeasonGameweeks.map((gw) => (
                    <SelectItem key={gw.id} value={gw.id}>
                      Gameweek {gw.GW} - {gw.season}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="kickoffTime">Kickoff Time</Label>
                <Input
                  id="kickoffTime"
                  type="datetime-local"
                  value={formData.kickoffTime}
                  onChange={(e) => handleInputChange("kickoffTime", e.target.value)}
                />
              </div>
            </div>

            {/* Teams Selection */}
            <div className="space-y-4">
              <Label>Teams</Label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {/* Home Team */}
                <div className="space-y-2">
                  <Label htmlFor="homeTeam">Home Team</Label>
                  <Select value={formData.teamHId} onValueChange={(value) => handleInputChange("teamHId", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select home team">
                        {selectedHomeTeam && (
                          <div className="flex items-center gap-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={selectedHomeTeam.logo} />
                              <AvatarFallback>{selectedHomeTeam.abbr}</AvatarFallback>
                            </Avatar>
                            <span>{selectedHomeTeam.name}</span>
                          </div>
                        )}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {teams.map((team) => (
                        <SelectItem key={team.id} value={team.id}>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={team.logo} />
                              <AvatarFallback>{team.abbr}</AvatarFallback>
                            </Avatar>
                            <span>{team.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Away Team */}
                <div className="space-y-2">
                  <Label htmlFor="awayTeam">Away Team</Label>
                  <Select value={formData.teamAId} onValueChange={(value) => handleInputChange("teamAId", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select away team">
                        {selectedAwayTeam && (
                          <div className="flex items-center gap-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={selectedAwayTeam.logo} />
                              <AvatarFallback>{selectedAwayTeam.abbr}</AvatarFallback>
                            </Avatar>
                            <span>{selectedAwayTeam.name}</span>
                          </div>
                        )}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {teams.map((team) => (
                        <SelectItem key={team.id} value={team.id}>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={team.logo} />
                              <AvatarFallback>{team.abbr}</AvatarFallback>
                            </Avatar>
                            <span>{team.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Scores */}
            <div className="space-y-4">
              <Label>Match Result (Optional)</Label>
              <div className="flex items-center gap-4 justify-center">
                <div className="flex items-center gap-2">
                  {selectedHomeTeam && (
                    <>
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={selectedHomeTeam.logo} />
                        <AvatarFallback>{selectedHomeTeam.abbr}</AvatarFallback>
                      </Avatar>
                      <span className="text-sm font-medium">{selectedHomeTeam.abbr}</span>
                    </>
                  )}
                </div>
                
                <Input
                  type="number"
                  min="0"
                  placeholder="0"
                  value={formData.teamHScore}
                  onChange={(e) => handleInputChange("teamHScore", e.target.value)}
                  className="w-16 text-center"
                />
                
                <span className="text-lg font-bold">-</span>
                
                <Input
                  type="number"
                  min="0"
                  placeholder="0"
                  value={formData.teamAScore}
                  onChange={(e) => handleInputChange("teamAScore", e.target.value)}
                  className="w-16 text-center"
                />
                
                <div className="flex items-center gap-2">
                  {selectedAwayTeam && (
                    <>
                      <span className="text-sm font-medium">{selectedAwayTeam.abbr}</span>
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={selectedAwayTeam.logo} />
                        <AvatarFallback>{selectedAwayTeam.abbr}</AvatarFallback>
                      </Avatar>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={saving || loading}>
            {saving ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
